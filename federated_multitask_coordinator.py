"""
联邦多任务优化协调器
管理多个任务的协同优化过程
"""

import numpy as np
from typing import Dict, List, Tuple, Callable, Optional
import concurrent.futures
import threading
import time
from federated_autosaea_server import FederatedAutoSAEAServer
from federated_autosaea_client import TaskSpecificAutoSAEA

class FederatedMultiTaskCoordinator:
    """联邦多任务优化协调器"""
    
    def __init__(self, global_bounds: np.ndarray, max_parallel_tasks: int = 4):
        self.global_bounds = global_bounds
        self.max_parallel_tasks = max_parallel_tasks
        
        # 服务器端
        self.server = FederatedAutoSAEAServer(num_tasks=0, global_bounds=global_bounds)
        
        # 任务管理
        self.tasks = {}  # task_id -> TaskSpecificAutoSAEA
        self.task_objectives = {}  # task_id -> objective_function
        self.task_configs = {}  # task_id -> configuration
        self.task_results = {}  # task_id -> results
        
        # 协调参数
        self.knowledge_sharing_frequency = 25  # 知识共享频率
        self.global_coordination_rounds = 10  # 全局协调轮数
        
        # 线程安全
        self.lock = threading.Lock()
        
        # 性能跟踪
        self.coordination_history = []
        self.task_interactions = {}
        
    def register_task(self, task_id: str, objective_func: Callable, 
                     task_type: str = "unknown", 
                     population_size: int = 30, max_fes: int = 300) -> bool:
        """注册新任务"""
        try:
            with self.lock:
                if task_id in self.tasks:
                    print(f"任务 {task_id} 已存在")
                    return False
                
                # 在服务器端注册任务
                self.server.register_task(task_id, task_type)
                
                # 创建任务特定的优化器
                task_optimizer = TaskSpecificAutoSAEA(
                    task_id=task_id,
                    objective_func=objective_func,
                    bounds=self.global_bounds,
                    population_size=population_size,
                    max_fes=max_fes
                )
                task_optimizer.task_type = task_type
                
                # 存储任务信息
                self.tasks[task_id] = task_optimizer
                self.task_objectives[task_id] = objective_func
                self.task_configs[task_id] = {
                    'task_type': task_type,
                    'population_size': population_size,
                    'max_fes': max_fes
                }
                
                print(f"任务 {task_id} (类型: {task_type}) 注册成功")
                return True
                
        except Exception as e:
            print(f"注册任务 {task_id} 失败: {e}")
            return False
    
    def optimize_all_tasks(self, parallel: bool = True) -> Dict:
        """优化所有任务"""
        if not self.tasks:
            print("没有注册的任务")
            return {}
        
        print(f"开始优化 {len(self.tasks)} 个任务...")
        print(f"并行模式: {parallel}")
        
        if parallel:
            return self._parallel_optimization()
        else:
            return self._sequential_optimization()
    
    def _parallel_optimization(self) -> Dict:
        """并行优化"""
        results = {}
        
        # 为所有任务提供初始知识
        self._initialize_all_tasks()
        
        # 创建线程池
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_parallel_tasks) as executor:
            # 提交所有任务
            future_to_task = {}
            for task_id, task_optimizer in self.tasks.items():
                future = executor.submit(self._optimize_single_task_with_coordination, 
                                       task_id, task_optimizer)
                future_to_task[future] = task_id
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_task):
                task_id = future_to_task[future]
                try:
                    best_x, best_fitness = future.result()
                    results[task_id] = {
                        'best_solution': best_x,
                        'best_fitness': best_fitness,
                        'statistics': self.tasks[task_id].get_task_statistics()
                    }
                    print(f"任务 {task_id} 完成，最佳适应度: {best_fitness:.6f}")
                except Exception as e:
                    print(f"任务 {task_id} 优化失败: {e}")
                    results[task_id] = {'error': str(e)}
        
        self.task_results = results
        return results
    
    def _sequential_optimization(self) -> Dict:
        """顺序优化"""
        results = {}
        
        # 为所有任务提供初始知识
        self._initialize_all_tasks()
        
        for task_id, task_optimizer in self.tasks.items():
            print(f"\n开始优化任务 {task_id}...")
            try:
                best_x, best_fitness = self._optimize_single_task_with_coordination(
                    task_id, task_optimizer)
                
                results[task_id] = {
                    'best_solution': best_x,
                    'best_fitness': best_fitness,
                    'statistics': task_optimizer.get_task_statistics()
                }
                
                print(f"任务 {task_id} 完成，最佳适应度: {best_fitness:.6f}")
                
                # 更新全局知识
                self._update_global_knowledge_from_task(task_id)
                
            except Exception as e:
                print(f"任务 {task_id} 优化失败: {e}")
                results[task_id] = {'error': str(e)}
        
        self.task_results = results
        return results
    
    def _initialize_all_tasks(self):
        """为所有任务提供初始知识"""
        for task_id, task_optimizer in self.tasks.items():
            # 从服务器获取初始化知识
            knowledge = self.server.get_task_initialization_knowledge(task_id)
            
            # 初始化任务
            task_optimizer.initialize_from_knowledge(knowledge)
            
            print(f"任务 {task_id} 初始化完成")
    
    def _optimize_single_task_with_coordination(self, task_id: str, 
                                              task_optimizer: TaskSpecificAutoSAEA) -> Tuple[np.ndarray, float]:
        """带协调的单任务优化"""
        
        def knowledge_callback(current_task_id: str, local_report: Dict) -> Dict:
            """知识回调函数"""
            return self._handle_knowledge_request(current_task_id, local_report)
        
        # 执行自适应优化
        best_x, best_fitness = task_optimizer.adaptive_optimize(
            server_knowledge_callback=knowledge_callback,
            knowledge_update_frequency=self.knowledge_sharing_frequency
        )
        
        return best_x, best_fitness
    
    def _handle_knowledge_request(self, task_id: str, local_report: Dict) -> Dict:
        """处理知识请求"""
        try:
            with self.lock:
                # 更新服务器知识
                self.server.update_task_knowledge(
                    task_id=task_id,
                    solutions=local_report['best_solutions'],
                    fitness_values=local_report['fitness_values'],
                    model_performance=local_report['model_performance'],
                    criterion_performance=local_report['criterion_performance']
                )
                
                # 获取更新的知识
                updated_knowledge = self.server.get_task_initialization_knowledge(task_id)
                
                # 获取推荐策略
                recommended_model, recommended_criterion = self.server.recommend_model_strategy(
                    task_id, local_report.get('total_fes', 0)
                )
                
                updated_knowledge['recommended_model'] = recommended_model
                updated_knowledge['recommended_criterion'] = recommended_criterion
                
                return updated_knowledge
                
        except Exception as e:
            print(f"处理任务 {task_id} 的知识请求失败: {e}")
            return {}
    
    def _update_global_knowledge_from_task(self, task_id: str):
        """从任务更新全局知识"""
        task_optimizer = self.tasks[task_id]
        report = task_optimizer.prepare_knowledge_report()
        
        self.server.update_task_knowledge(
            task_id=task_id,
            solutions=report['best_solutions'],
            fitness_values=report['fitness_values'],
            model_performance=report['model_performance'],
            criterion_performance=report['criterion_performance']
        )
    
    def analyze_task_relationships(self) -> Dict:
        """分析任务关系"""
        if not hasattr(self.server.knowledge_base, 'task_similarities'):
            return {}
        
        similarity_info = self.server.knowledge_base.task_similarities
        if not similarity_info:
            return {}
        
        task_ids = similarity_info['task_ids']
        similarity_matrix = similarity_info['matrix']
        
        # 找出高相似性任务对
        high_similarity_pairs = []
        threshold = 0.7
        
        for i in range(len(task_ids)):
            for j in range(i + 1, len(task_ids)):
                if similarity_matrix[i, j] > threshold:
                    high_similarity_pairs.append({
                        'task1': task_ids[i],
                        'task2': task_ids[j],
                        'similarity': similarity_matrix[i, j]
                    })
        
        # 任务聚类
        task_clusters = self._cluster_tasks(task_ids, similarity_matrix)
        
        return {
            'similarity_matrix': similarity_matrix.tolist(),
            'task_ids': task_ids,
            'high_similarity_pairs': high_similarity_pairs,
            'task_clusters': task_clusters,
            'task_relationships': dict(self.server.task_relationships)
        }
    
    def _cluster_tasks(self, task_ids: List[str], similarity_matrix: np.ndarray) -> Dict:
        """任务聚类"""
        if len(task_ids) < 2:
            return {'clusters': [task_ids], 'cluster_labels': [0] * len(task_ids)}
        
        try:
            from sklearn.cluster import AgglomerativeClustering
            
            # 将相似性转换为距离
            distance_matrix = 1.0 - similarity_matrix
            
            # 层次聚类
            n_clusters = min(3, len(task_ids))  # 最多3个聚类
            clustering = AgglomerativeClustering(
                n_clusters=n_clusters,
                metric='precomputed',
                linkage='average'
            )
            
            cluster_labels = clustering.fit_predict(distance_matrix)
            
            # 组织聚类结果
            clusters = {}
            for i, label in enumerate(cluster_labels):
                if label not in clusters:
                    clusters[label] = []
                clusters[label].append(task_ids[i])
            
            return {
                'clusters': list(clusters.values()),
                'cluster_labels': cluster_labels.tolist(),
                'n_clusters': n_clusters
            }
            
        except ImportError:
            # 简单的基于阈值的聚类
            clusters = []
            used_tasks = set()
            
            for i, task1 in enumerate(task_ids):
                if task1 in used_tasks:
                    continue
                
                cluster = [task1]
                used_tasks.add(task1)
                
                for j, task2 in enumerate(task_ids):
                    if i != j and task2 not in used_tasks and similarity_matrix[i, j] > 0.6:
                        cluster.append(task2)
                        used_tasks.add(task2)
                
                clusters.append(cluster)
            
            return {'clusters': clusters, 'cluster_labels': None}
    
    def get_optimization_summary(self) -> Dict:
        """获取优化总结"""
        if not self.task_results:
            return {'error': '没有优化结果'}
        
        summary = {
            'total_tasks': len(self.tasks),
            'successful_tasks': len([r for r in self.task_results.values() if 'error' not in r]),
            'failed_tasks': len([r for r in self.task_results.values() if 'error' in r]),
            'task_results': {},
            'global_statistics': {},
            'task_relationships': self.analyze_task_relationships()
        }
        
        # 任务结果汇总
        for task_id, result in self.task_results.items():
            if 'error' not in result:
                summary['task_results'][task_id] = {
                    'best_fitness': result['best_fitness'],
                    'total_fes': result['statistics']['total_fes'],
                    'convergence_status': result['statistics']['convergence_assessment']['status'],
                    'adaptation_effectiveness': result['statistics']['adaptation_effectiveness']
                }
        
        # 全局统计
        if summary['successful_tasks'] > 0:
            all_fitness = [r['best_fitness'] for r in self.task_results.values() if 'error' not in r]
            all_fes = [r['statistics']['total_fes'] for r in self.task_results.values() if 'error' not in r]
            
            summary['global_statistics'] = {
                'best_fitness_overall': min(all_fitness),
                'worst_fitness_overall': max(all_fitness),
                'average_fitness': np.mean(all_fitness),
                'fitness_std': np.std(all_fitness),
                'average_fes': np.mean(all_fes),
                'total_fes': sum(all_fes)
            }
        
        return summary
    
    def save_results(self, filename: str):
        """保存结果"""
        import json
        
        summary = self.get_optimization_summary()
        
        # 转换numpy数组为列表以便JSON序列化
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        summary_serializable = convert_numpy(summary)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary_serializable, f, indent=2, ensure_ascii=False)
        
        print(f"结果已保存到 {filename}")
    
    def print_summary(self):
        """打印优化总结"""
        summary = self.get_optimization_summary()
        
        print("\n" + "="*60)
        print("联邦多任务优化总结")
        print("="*60)
        
        print(f"总任务数: {summary['total_tasks']}")
        print(f"成功任务数: {summary['successful_tasks']}")
        print(f"失败任务数: {summary['failed_tasks']}")
        
        if summary['successful_tasks'] > 0:
            print(f"\n全局统计:")
            stats = summary['global_statistics']
            print(f"  最佳适应度: {stats['best_fitness_overall']:.6f}")
            print(f"  平均适应度: {stats['average_fitness']:.6f}")
            print(f"  适应度标准差: {stats['fitness_std']:.6f}")
            print(f"  平均FEs: {stats['average_fes']:.0f}")
            print(f"  总FEs: {stats['total_fes']}")
            
            print(f"\n各任务结果:")
            for task_id, result in summary['task_results'].items():
                print(f"  {task_id}:")
                print(f"    最佳适应度: {result['best_fitness']:.6f}")
                print(f"    FEs: {result['total_fes']}")
                print(f"    收敛状态: {result['convergence_status']}")
                print(f"    适应效果: {result['adaptation_effectiveness']:.3f}")
        
        # 任务关系分析
        if 'task_relationships' in summary and summary['task_relationships']:
            relationships = summary['task_relationships']
            if 'high_similarity_pairs' in relationships and relationships['high_similarity_pairs']:
                print(f"\n高相似性任务对:")
                for pair in relationships['high_similarity_pairs']:
                    print(f"  {pair['task1']} - {pair['task2']}: {pair['similarity']:.3f}")
        
        print("="*60)
