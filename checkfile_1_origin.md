

<!-- Meanless: 1114 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->

# Surrogate-Assisted Evolutionary Algorithm With Model and Infill Criterion Auto-Configuration

Lindong Xie \( {}^{\circledR } \) , <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> \( {}^{\circledR } \) , Member, IEEE, <PERSON>z<PERSON> \( {}^{\circledR } \) , Senior Member, IEEE, and Maoguo Gong \( {}^{ - } \) ,Senior Member, IEEE

Abstract-Surrogate-assisted evolutionary algorithms (SAEAs) have proven to be effective in solving computationally expensive optimization problems (EOPs). However, the performance of SAEAs heavily relies on the surrogate model and infill criterion used. To improve the generalization of SAEAs and enable them to solve a wide range of EOPs, this article proposes an SAEA called AutoSAEA, which features model and infill criterion auto-configuration. Specifically, AutoSAEA formulates model and infill criterion selection as a two-level multiarmed bandit problem (TL-MAB). The first and second levels cooperate in selecting the surrogate model and infill criterion, respectively. A two-level reward (TL-R) measures the value of the surrogate model and infill criterion, while a two-level upper confidence bound (TL-UCB) selects the model and infill criterion in an online manner. Numerous experiments validate the superiority of AutoSAEA over some state-of-the-art SAEAs on complex benchmark problems and a real-world oil reservoir production optimization problem.

Index Terms-Auto algorithm design, expensive optimization, surrogate-assisted evolutionary algorithm (SAEA), two-level multiarmed bandit (TL-MAB).

## I. INTRODUCTION

ID XPENSIVE optimization problems (EOPs) are prevalent in many engineering designs and applications (e.g., aerodynamic structures design [1], automobile crash analysis [2], and space tethered-net system design [3]). Unfortunately, solving such problems requires conducting computationally expensive computer simulations or costly physics experiments since the analytical expressions of the problem are unavailable. Generally, the EOP can be expressed as follows:

\[\min f\left( \mathbf{x}\right) \]

\[\text{s.t.}{\mathbf{x}}_{l} \leq  \mathbf{x} \leq  {\mathbf{x}}_{u} \tag{1}\]

where \( \mathbf{x} = {\left( {x}_{1},\ldots ,{x}_{D}\right) }^{\top } \) is the decision vector of \( D \) variables, \( {\mathbf{x}}_{l} = {\left( {x}_{l,1},\ldots ,{x}_{l,D}\right) }^{\top } \) and \( {\mathbf{x}}_{u} = {\left( {x}_{u,1},\ldots ,{x}_{u,D}\right) }^{\top } \) are the lower and upper bounds of the search space,respectively,and \( f\left( \mathbf{x}\right) \) denotes a scalar objective function. We assume that the analytical expression of \( f\left( \mathbf{x}\right) \) is unavailable and the calculation of \( f\left( \mathbf{x}\right) \) is costly.

Evolutionary algorithms have been shown to be popular and effective for solving black-box optimization problems [4], [5]. However, they often perform poorly on EOPs due to the large number of function evaluations (FEs) required in their optimization process. This limitation becomes more significant in the case of EOPs [6]. To address this issue, surrogate-assisted evolutionary algorithms (SAEAs) have been proposed. In SAEAs, the optimization is mainly driven by computationally cheap surrogate models. The popular surrogate models used in SAEAs include regression models, such as radial basis function (RBF) [7], Gaussian process model (GP) [8], and polynomial response surface (PRS) [9],and classification models,such as \( k \) -nearest neighbor (KNN) [10] and support vector machine model (SVM) [11]. Based on the adopted surrogate model, existing SAEAs can be roughly grouped into three categories: 1) single-model SAEAs [12], [13], [14], [15]; 2) multiple-model SAEAs [16], [17], [18], [19], [20], [21]; and 3) adaptive-model SAEAs [22], [23], [24], [25].

Single-model SAEAs use a fixed surrogate model throughout the optimization process, typically combining a fixed infill criterion, such as GP with expected improvement (EI) [26] and lower confidence bound (LCB) [27], RBF with local search [28], or KNN with prescreening [29], to select candidate solutions for FEs. However, due to the limited training samples available and the inability of a single model to effectively fit various problem landscapes, single-model SAEAs often struggle to solve different EOPs effectively [18], [30].

In order to enhance the robustness and scalability of single-model SAEAs, multiple-model SAEAs have been developed, including hierarchical-model SAEAs and ensemble-model SAEAs [31]. Hierarchical-model SAEAs commonly combine a global model and a local model [16], [18], [32], [33], [34]. The global model approximates the fitness landscape to search for promising regions, and the local model is used to conduct a refined search in the found local promising regions. Ensemble-model SAEAs typically train several different base surrogate models to cooperatively implement a more precise prediction [9], [19], [20], [21]. Generally, multiple-model SAEAs outperform single-model ones in most cases [15]. However, the computational cost of multiple-model SAEAs is always higher compared to the single-model SAEAs [35], [36]. Additionally, some base models may not fit the problem landscape well, so their efficiency may still be low.

---

<!-- Footnote -->

Manuscript received 28 January 2023; revised 7 May 2023; accepted 27 June 2023. Date of publication 3 July 2023; date of current version 1 August 2024. This work was supported in part by the National Natural Science Foundation of China under Grant 62206120, Grant 62106096, and Grant 62036006; and in part by the Shenzhen Technology Plan under Grant JCYJ20220530113013031. (Lindong Xie and Genghui Li contributed equally to this work.) (Corresponding author: Zhenkun Wang.)

Lindong Xie and Genghui Li are with the School of System Design and Intelligent Manufacturing, Southern University of Science and Technology, Shenzhen 518055, China (e-mail: <EMAIL>; <EMAIL>).

Zhenkun Wang is with the School of System Design and Intelligent Manufacturing and the Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China (e-mail: <EMAIL>).

Laizhong Cui is with the College of Computer Science and Software Engineering and the Guangdong Laboratory of Artificial Intelligence and Digital Economy (SZ), Shenzhen University, Shenzhen 518060, China (e-mail: <EMAIL>).

Maoguo Gong is with the Key Laboratory of Collaborative Intelligence Systems, Ministry of Education, Xidian University, Xi'an 710071, China (e-mail: <EMAIL>).

This article has supplementary material provided by the authors and color versions of one or more figures available at https://doi.org/10.1109/ TEVC.2023.3291614.

Digital Object Identifier 10.1109/TEVC.2023.3291614

<!-- Footnote -->

---

<!-- Meanless: 1089-778X (C) 2023 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information. Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: XIE et al.: SAEA 1115-->

To leverage well-established models effectively and reduce computational complexity, adaptive-model SAEAs have been proposed that automatically select the suitable model (or infill criterion) in an online manner [22], [24], [25]. However, the performance of such algorithms dramatically depends on the design of the adaptive selection strategy. It is well known that the surrogate model is tightly coupled with the infill criterion in SAEAs, and they cooperatively affect the performance of SAEAs. A model with different infill criteria can realize different balances of exploiting better solutions and exploring unexplored search regions. Naturally, a better balance between exploration and exploitation may be achieved by cooperatively considering the model and the infill criterion in a hierarchical coupled way. Unfortunately, existing adaptive-model SAEAs are either only for model or infill criterion selection but do not study adaptive selection for both cooperatively. Therefore, to improve the performance of SAEAs by filling this gap, this article proposes an SAEA (called AutoSAEA) with the model and infill criterion auto-configuration by using the hierarchical multiarmed bandit (MAB) method.

The contributions of this article are summarized as follows.

1) Formulating the surrogate model and infill criterion cooperative selection as a two-level MAB problem (TL-MAB) [37], [38], [39].

2) Designing a two-level reward (TL-R) to measure the utility of the surrogate model and infill criterion in a cooperative manner.

3) Adopting a two-level upper confidence bound (TL-UCB) to select the surrogate model and infill criterion cooperatively in an online manner.

4) Proposing an SAEA with a surrogate model and infill criterion auto-configuration, and verifying its advantages by comparing it with some state-of-the-art SAEAs on two sets of complex benchmark problems and a real-world problem.

The remainder of this article is organized as follows. Section II reviews the related work. Section III introduces the background knowledge involved in the proposed algorithm. Section IV describes the proposed algorithm in detail. Section V presents the numerical experiments conducted to validate the effectiveness of the proposed algorithm. Finally, Section VI summarizes this article and discusses future work directions.

## II. RELATED WORK

1) Single-Model SAEAs: Single-model SAEAs can either train a global model using all evaluated solutions or a local model with some good ones to predict the quality of new solutions. For example, the Bayesian optimization framework is a classic and popular single-model method [40], [41], [42]. It uses the GP model to build a surrogate for the objective and quantify the uncertainty in the surrogate. Then, it optimizes an acquisition function (e.g., EI [40] and LCB [43]) defined from the surrogate to decide the new solution for evaluation. GPEME [13] first constructs a local GP model in the lower-dimensional space, followed by the application of prescreening with LCB to determine which offspring can be selected for FE. CPS-MOEA [44] employs a local KNN classifier to filter out potentially nondominated solutions for real evaluation. SA-COSO [45] evaluates the new position of the particle that has the minimum fitness value predicted by a global RBF model. In addition, SHPSO [46] and SAMSO [15] construct a local RBF model and a global RBF model, respectively, to select particles with predicted fitness values that are better than their personal best ones for FEs. MGP-SLPSO [14] formulates the approximated fitness and its corresponding uncertainty provided by a global GP model as a bi-objective problem and applies a nondominated sorting method to select the offspring for FEs. Furthermore, CA-LLSO [47] trains a local gradient boosting classifier to predict the level of the offspring produced by the level-based learning swarm optimizer.

2) Multiple-Model SAEAs: Multiple-model SAEAs mainly combine a global and a local model or train multiple models simultaneously to balance exploration and exploitation. For example, CAL-SAPSO [19] identifies the best and most uncertain solutions for FEs using a global ensemble surrogate model. Additionally, the optimum of the local ensemble model found by the local search is also used for expensive evaluation. HeE-MOEA [35] utilizes an SVM and two RBF models to build an ensemble model, which is combined with LCB and EI criteria to screen the offspring for expensive evaluations. ESAO [32] employs a global RBF to screen the offspring with the minimum predicted fitness value for FE. Besides, the optimum of the local RBF model found by the differential evolution (DE) is also evaluated. GSGA [33] adopts a local RBF-assisted trust-region method and a global GP-assisted genetic algorithm for exploitation and exploration, respectively. GL-SADE [18] trains a global RBF model and a local GP model to select offspring for FEs. Moreover, when the local GP model finds the current best solution, DE is applied further to search for its optimal solution. ESCO [21] constructs multiple RBF models on various low-dimensional sample sets, and then selects the models with superior performance to compose an ensemble surrogate.

3) Adaptive-Model SAEAs: Adaptive-model SAEAs always design an adaptive selection strategy to automatically choose a model or an infill criterion in an online manner. For example, GP-Hedge [22] adaptively selects an appropriate acquisition function from a portfolio of well-established ones, such as the probability of improvement, EI, and UCB. This selection is based on an online MAB strategy. Specifically, the optimal solutions of all acquisition functions form a candidate solution pool, from which solutions are selected for FEs based on the cumulative rewards of their respective acquisition functions. While GP-Hedge only adaptively selects the acquisition functions, it fixes the surrogate model. Moreover, its computational efficiency is low since it needs to optimize multiple acquisition functions in each generation. ASMEA [30] first constructs a base model pool using GP, RBF, PRS, and linear Shepard interpolation. It then uses the prediction residual error sum of squares (PRESS) and root mean square error (RMSE) to select several meta-models from the base model pool for constructing five ensemble models, which are then included in the base model pool. Finally, a promising surrogate model is adaptively selected from the base model pool based on its minimum RMSE. While ASMEA only adaptively selects the models, it fixes the pre-screening as the infill criterion. Moreover, its computational efficiency is low since it needs to conduct cross-validation for each base model. ESA [25] first constructs a pool of four different infill criteria, including DE evolutionary screening, surrogate-assisted local search, full-crossover strategy, and surrogate-assisted trust-region local search. Among them, the DE evolutionary screening strategy prefers exploration, the surrogate-assisted local search and trust-region local search strategies use different local search methods to favor exploitation, and the full-crossover strategy integrates good genes from historical solutions. Moreover, \( Q \) -learning is used to adjust the selection probability of each infill criterion through feedback information received during the optimization process. However, ESA fixes the RBF as the surrogate model.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 1116 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->

Additionally, some adaptive-model SAEAs have been proposed to deal with expensive multiobjective optimization problems. For instance, KTA2 [23] divides the optimization process into three states (i.e., convergence-demand, diversity-demand, and uncertainty-demand states) and uses the distances from solutions to the estimated ideal point and the pure diversity indicator to estimate the state. Moreover, the infill criterion is adaptively chosen based on the estimated optimization state, which guides the solution selection for FEs by taking into account the requirements on convergence, diversity, and model uncertainty separately. RVMM [48] uses the GP as the surrogate model and develops an adaptive model management strategy to adaptively choose the convergence-related criterion and diversity-related criterion. The adaptive model management strategy is assisted by two sets of reference vectors. One set of adaptive reference vectors focuses on convergence, while the other set of fixed reference vectors concentrates on diversity. The GP and RBF models are the most popular surrogate models due to their high fidelity and simplicity. Generally, the RBF model is computationally more efficient than the GP model. However, the GP model can provide uncertain information about its predictions. Therefore, to take advantage of the GP and RBF models in dealing with different problems, IBEA-MS [24] designs an acceptable reliability tolerance criterion to adaptively determine whether to use the GP model or the RBF model in environmental selection. Specifically, when the uncertainty of the GP model exceeds the acceptable error, the RBF model is used. Otherwise, the GP models are used.

## III. BACKGROUND

## A. Surrogate Models

1) GP Model: Given a training data set \( {\left\{  {\mathbf{x}}_{i},f\left( {\mathbf{x}}_{i}\right) \right\}  }_{i = 1}^{N} \) ,GP predicts the output \( {f}_{\mathrm{{GP}}}\left( \mathbf{x}\right) \) of a solution \( \mathbf{x} \) in the way of: \( {f}_{\mathrm{{GP}}}\left( \mathbf{x}\right)  = \mu \left( \mathbf{x}\right)  + \epsilon \left( \mathbf{x}\right) \) ,where \( \mu \left( \mathbf{x}\right) \) represents a global trend of the training data,and \( \epsilon \left( \mathbf{x}\right)  \sim  \mathcal{N}\left( {0,{\sigma }^{2}\left( \mathbf{x}\right) }\right) \) is a normal distribution. \( \mu \left( \mathbf{x}\right) \) and \( {\sigma }^{2}\left( \mathbf{x}\right) \) are estimated as follows [40]:

\[\mu \left( \mathbf{x}\right)  = \mathbf{k}{\left( \mathbf{x}\right) }^{\top }{\mathbf{K}}^{-1}\mathbf{F} \tag{2}\]

\[{\sigma }^{2}\left( \mathbf{x}\right)  = \kappa \left( \mathbf{x}\right)  - \mathbf{k}{\left( \mathbf{x}\right) }^{\top }{\mathbf{K}}^{-1}\mathbf{k}\left( \mathbf{x}\right)  \tag{3}\]

where \( \mathbf{k}\left( \mathbf{x}\right)  = {\left\lbrack  C\left( \mathbf{x},{\mathbf{x}}_{1}\right) ,\ldots ,C\left( \mathbf{x},{\mathbf{x}}_{N}\right) \right\rbrack  }^{\top };\mathbf{K} \) is an \( N \times  N \) matrix and \( {K}_{i,j} = C\left( {{\mathbf{x}}_{i},{\mathbf{x}}_{j}}\right) ;\kappa \left( \mathbf{x}\right)  = C\left( {\mathbf{x},\mathbf{x}}\right) \) ,and \( \mathbf{F} = \) \( {\left\lbrack  f\left( {\mathbf{x}}_{1}\right) ,\ldots ,f\left( {\mathbf{x}}_{N}\right) \right\rbrack  }^{\top } \) . The covariance function \( C\left( {\cdot , \cdot  }\right) \) can be commonly calculated as follows:

\[C\left( {{\mathbf{x}}_{i},{\mathbf{x}}_{j}}\right)  = \exp \left( {-\mathop{\sum }\limits_{{d = 1}}^{D}{\theta }_{d}{\left| {x}_{i,d} - {x}_{j,d}\right| }^{2}}\right)  \tag{4}\]

where the hyper-parameters \( \theta \) can be obtained by maximizing the likelihood function [33].

Finally,for an unknown solution \( \mathbf{x} \) ,its predicted fitness value \( {\widehat{f}}_{\mathrm{{GP}}}\left( \mathbf{x}\right) \) and uncertainty \( \widehat{s}\left( \mathbf{x}\right) \) are given in the following:

\[{\widehat{f}}_{\mathrm{{GP}}}\left( \mathbf{x}\right)  = \mu \left( \mathbf{x}\right)  + \mathbf{k}{\left( \mathbf{x}\right) }^{\top }{\mathbf{K}}^{-1}\left( {\mathbf{F} - \mathbf{I}\mu \left( \mathbf{x}\right) }\right)  \tag{5}\]

\[{\widehat{s}}^{2}\left( \mathbf{x}\right)  = {\sigma }^{2}\left( \mathbf{x}\right) \left\lbrack  {1 - \mathbf{k}{\left( \mathbf{x}\right) }^{\top }{\mathbf{K}}^{-1}\mathbf{k}\left( \mathbf{x}\right)  + \frac{{\left( 1 - {\mathbf{I}}^{\top }{\mathbf{K}}^{-1}\mathbf{k}\left( \mathbf{x}\right) \right) }^{2}}{{\mathbf{I}}^{\top }{\mathbf{K}}^{-1}\mathbf{I}}}\right\rbrack  \]

(6)

where \( \mathbf{I} \) is an \( N \times  1 \) unit vector,and \( \widehat{s}\left( \mathbf{x}\right)  = \sqrt{{\widehat{s}}^{2}\left( \mathbf{x}\right) } \) is the RMSE.

2) RBF Model: The RBF model uses a linear combination of basis functions to approximate the fitness landscape. For the given training data set \( {\left\{  {\mathbf{x}}_{i},f\left( {\mathbf{x}}_{i}\right) \right\}  }_{i = 1}^{N} \) ,the form of the RBF model can be formulated as follows [49]:

\[{\widehat{f}}_{\mathrm{{RBF}}}\left( \mathbf{x}\right)  = \mathop{\sum }\limits_{{i = 1}}^{N}{w}_{i}\varphi \left( \begin{Vmatrix}{\mathbf{x} - {\mathbf{x}}_{i}}\end{Vmatrix}\right)  + {b}_{0} + \mathop{\sum }\limits_{{j = 1}}^{D}{b}_{j}{x}_{j} \tag{7}\]

where \( \mathbf{w} = {\left( {w}_{1},{w}_{2},\ldots ,{w}_{N}\right) }^{\top } \) is the weight vector of the basis function, and this article adopts the cubic function \( \varphi \left( \begin{Vmatrix}{\mathbf{x} - {\mathbf{x}}_{i}}\end{Vmatrix}\right)  = {\left( \begin{Vmatrix}\mathbf{x} - {\mathbf{x}}_{i}\end{Vmatrix}\right) }^{3} \) ,where \( \parallel  \cdot  \parallel \) denotes the Euclidean distance. \( \mathbf{b} = {\left( {b}_{0},{b}_{1},\ldots ,{b}_{D}\right) }^{\top } \) is the coefficient of the first-order polynomial.

The parameters \( \mathbf{w} \) and \( \mathbf{b} \) in (7) can be obtained as follows:

\[\left\lbrack  \begin{array}{l} \mathbf{w} \\  \mathbf{b} \end{array}\right\rbrack   = {\left\lbrack  \begin{matrix} \mathbf{\Phi } & \mathbf{P} \\  {\mathbf{P}}^{\top } & {\mathbf{0}}_{\left( {D + 1}\right)  \times  \left( {D + 1}\right) } \end{matrix}\right\rbrack  }^{ \dagger  }\left\lbrack  \begin{matrix} \mathbf{F} \\  {\mathbf{0}}_{\left( {D + 1}\right)  \times  1} \end{matrix}\right\rbrack   \tag{8}\]

where \( {\Phi }_{i,j} = \varphi \left( {\begin{Vmatrix}{\mathbf{x}}_{i} - {\mathbf{x}}_{j}\end{Vmatrix}}_{2}\right) ,i,j = 1,2,\ldots ,N;\mathbf{P} = \) \( {\left\lbrack  {\mathbf{P}}_{1},\ldots ,{\mathbf{P}}_{N}\right\rbrack  }^{\top } \) and \( {\mathbf{P}}_{i} = {\left\lbrack  1,{x}_{i,1},\ldots ,{x}_{i,D}\right\rbrack  }^{\top };{\mathbf{0}}_{\left( {D + 1}\right)  \times  \left( {D + 1}\right) } \) is a zero matrix of \( \left( {D + 1}\right)  \times  \left( {D + 1}\right) \) ,and \( \dagger \) is the generalized inverse,and \( \mathbf{F} = {\left\lbrack  f\left( {\mathbf{x}}_{1}\right) ,\ldots ,f\left( {\mathbf{x}}_{N}\right) \right\rbrack  }^{\top } \) .

3) PRS Model: The commonly used second-order polynomial is defined as follows [9]:

\[{\widehat{f}}_{\mathrm{{PRS}}}\left( \mathbf{x}\right)  = {\beta }_{0} + \mathop{\sum }\limits_{{i = 1}}^{D}{\beta }_{i}{x}_{i} + \mathop{\sum }\limits_{{i = 1}}^{D}\mathop{\sum }\limits_{{j \geq  i}}^{D}{\beta }_{ij}{x}_{i}{x}_{j} \tag{9}\]

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: XIE et al.: SAEA 1117-->

where coefficients \( {\beta }_{0},{\beta }_{i} \) ,and \( {\beta }_{ij} \) are regression parameters for the intercept, the linear term, and the quadratic term, respectively.

For the given training data set \( {\left\{  {\mathbf{x}}_{i},f\left( {\mathbf{x}}_{i}\right) \right\}  }_{i = 1}^{N} \) ,the unknown coefficients of the above polynomial model \( \mathbf{\beta } = \left( {{\beta }_{0},\ldots }\right. \) , \( {\beta }_{D},{\beta }_{1,1},\ldots ,{\beta }_{1,D},{\beta }_{2,3},\ldots ,{\beta }_{2,D},\ldots ,{\beta }_{D - 1,D},{\beta }_{D,D}{)}^{\top } \) can be obtained by the least square method as follows [50]:

\[\mathbf{\beta } = {\left( {\mathbf{P}}^{\top }\mathbf{P}\right) }^{-1}{\mathbf{P}}^{\top }\mathbf{F} \tag{10}\]

where \( \mathbf{P} = {\left\lbrack  {\mathbf{P}}_{1},\ldots ,{\mathbf{P}}_{N}\right\rbrack  }^{\top },{\mathbf{P}}_{i} = {\left\lbrack  1,{x}_{i,1},\ldots ,{x}_{i,D}\right\rbrack  }^{\top } \) ,and \( \mathbf{F} = \) \( {\left\lbrack  f\left( {\mathbf{x}}_{1}\right) ,\ldots ,f\left( {\mathbf{x}}_{N}\right) \right\rbrack  }^{\top } \) .

4) KNN Model: KNN is a popular classifier [29], [44]. Based on a training data set \( {\left\{  {\mathbf{x}}_{i},f\left( {\mathbf{x}}_{i}\right) ,y\left( {\mathbf{x}}_{i}\right) \right\}  }_{i = 1}^{N} \) ,where \( f\left( {\mathbf{x}}_{i}\right) \) and \( y\left( {\mathbf{x}}_{i}\right) \) denote the objective function value and class/level of the solution \( {\mathbf{x}}_{i} \) ,respectively. For a new solution \( \mathbf{x} \) ,its class/level is predicted as follows:

\[{y}_{\mathrm{{KNN}}}\left( \mathbf{x}\right)  = \operatorname{mode}\left( \left\{  {y\left( {\mathbf{x}}_{1}\right) ,\ldots ,y\left( {\mathbf{x}}_{K}\right) }\right\}  \right)  \tag{11}\]

where \( {\mathbf{x}}_{1},\ldots ,{\mathbf{x}}_{K} \) are \( K \) nearest neighbors of \( \mathbf{x} \) ,and mode(   ) denotes the mode of a set. In this article, \( K \) is set to 1 [29],[44],which means that the new solution \( \mathbf{x} \) is assigned the same class as its nearest neighbor.

In this article, all surrogate models are trained based on the current population \( \mathcal{P} \) in each iteration.

## B. Infill Criteria

Since the surrogate models are tightly coupled with the infill criteria in SAEAs, the corresponding infill criteria for the surrogate models mentioned in the previous section are introduced as follows.

1) Infill Criteria for the GP Model: The LCB and EI criteria are commonly combined with GP for determining new solutions for FEs, which are expressed sequentially as follows [13], [33]:

\[{\mathbf{x}}^{ * } = \underset{\mathbf{x} \in  \mathcal{X}}{\arg \min }\left( {{\widehat{f}}_{\mathrm{{GP}}}\left( \mathbf{x}\right)  - w\widehat{s}\left( \mathbf{x}\right) }\right)  \tag{12}\]

where \( w \) is set to 2 in this article [13],[27]. \( \mathcal{X} \) denotes a set of unknown solutions.

\[{\mathbf{x}}^{ * } = \underset{\mathbf{x} \in  \mathcal{X}}{\arg \max }\left( {\left( {{f}_{\min } - {\widehat{f}}_{\mathrm{{GP}}}\left( \mathbf{x}\right) }\right) \Phi \left( Z\right)  + \widehat{s}\left( \mathbf{x}\right) \varphi \left( Z\right) }\right)  \tag{13}\]

where \( Z = \left( {\left\lbrack  {{f}_{\min } - {\widehat{f}}_{\mathrm{{GP}}}\left( \mathbf{x}\right) }\right\rbrack  /\left\lbrack  {\widehat{s}\left( \mathbf{x}\right) }\right\rbrack  }\right) ,{f}_{\min } \) is the current minimum objective function value,and \( \Phi \left( \cdot \right) \) and \( \varphi \left( \cdot \right) \) are the density and cumulative distribution functions of standard normal distribution, respectively.

This article adopts the DE mutation and crossover operators to generate the new solution set \( \mathcal{X} = \left\{  {{\mathbf{o}}_{1},\ldots ,{\mathbf{o}}_{N}}\right\} \) ,where \( N \) is the population size. To be specific, assume that the current population is \( \mathcal{P},{\mathbf{o}}_{i} = \left( {{o}_{i,1},\ldots ,{o}_{i,D}}\right) \) is generated as follows:

\[{\mathbf{v}}_{i} = {\mathbf{x}}_{i} + F \times  \left( {{\mathbf{x}}_{b} - {\mathbf{x}}_{i}}\right)  + F \times  \left( {{\mathbf{x}}_{r1} - {\mathbf{x}}_{r2}}\right)  \tag{14}\]

\[{o}_{i,j} = \left\{  \begin{array}{l} {v}_{i,j},\text{ if }{\operatorname{rand}}_{i,j} \leq  {CR}\text{ or }j = {j}_{\text{rand }} \\  {x}_{i,j},\text{ otherwise } \end{array}\right.  \tag{15}\]

where \( {\mathbf{x}}_{i} \) is the \( i \) th solution in \( \mathcal{P},{\mathbf{x}}_{r1} \) and \( {\mathbf{x}}_{r2} \) are randomly selected from the current population \( \mathcal{P},{\mathbf{x}}_{b} \) is the best solution of \( \mathcal{P};F \) is the scale factor,and it is set to 0.5 in this article. rand \( {}_{i,j} \) and \( {j}_{\text{rand }} \) are randomly selected from \( \left\lbrack  {0,1}\right\rbrack \) and \( \{ 1,\ldots ,D\} \) ,respectively,and \( {CR} \in  \left\lbrack  {0,1}\right\rbrack \) denotes the crossover rate, which is set to 0.9 in this article.

Note that if \( {o}_{i,j} \) violates the boundary constraints,it will be repaired as follows:

\[{o}_{i,j} = {x}_{l,j} + \operatorname{rand} \times  \left( {{x}_{u,j} - {x}_{l,j}}\right) . \tag{16}\]

2) Infill Criteria for the RBF and PRS Models: For all regression models (e.g., RBF and PRS), prescreening and local search are widely used in SAEAs, which are formulated as follows [28], [51]:

\[{\mathbf{x}}^{ * } = \underset{\mathbf{x} \in  \mathcal{X}}{\arg \min }{\widehat{f}}_{\mathrm{{RBF}}/\mathrm{{PRS}}}\left( \mathbf{x}\right)  \tag{17}\]

where \( \mathbf{x} \) and \( {\widehat{f}}_{\mathrm{{RBF}}/\mathrm{{PRS}}}\left( \mathbf{x}\right) \) denote the unknown solution and its predicted fitness value by RBF/PRS, respectively. It should be noted that if \( \mathcal{X} \) is a finite set of solutions,(17) belongs to prescreening,and it denotes a local search if \( \mathcal{X} \) is a subspace of the search space.

In this article, the above DE operator is used to generate \( \mathcal{X} \) for the prescreening. For the local search, \( \mathcal{X} \) is defined as follows:

\[\mathcal{X} = \left\lbrack  {\mathbf{{lb}},\mathbf{{ub}}}\right\rbrack   \tag{18}\]

where \( \mathbf{{lb}} = {\left( {l}_{1},\ldots ,{l}_{D}\right) }^{\top } \) and \( \mathbf{{ub}} = {\left( {u}_{1},\ldots ,{u}_{D}\right) }^{\top },{l}_{j} = \) \( \min \left\{  {{x}_{i,j},{\mathbf{x}}_{i} \in  \mathcal{P}}\right\} \) ,and \( {u}_{j} = \max \left\{  {{x}_{i,j},{\mathbf{x}}_{i} \in  \mathcal{P}}\right\}  ,j = 1,\ldots ,D \) . Moreover, DE is used to solve (17) in the local search in this article.

3) Infill Criteria for the KNN Model: Classification models are often used to predict the level of the unknown solutions [52]. To this end, the current population is evenly divided into \( L \) levels \( {\mathcal{P}}^{1},\ldots ,{\mathcal{P}}^{L} \) based on the fitness,and the best \( \left( {N/L}\right) \) solutions belong to the first level \( {\mathcal{P}}^{1} \) . Two infill criteria (e.g., L1-exploitation and L1-exploration) have been designed for the classifier model. They are defined as follows [47].

1) L1-Exploitation: The L1-exploitation criterion is defined as

\[{\mathbf{x}}^{ * } = \arg \mathop{\min }\limits_{{\mathbf{x} \in  {\mathcal{X}}^{1}}}\max \left\{  {\parallel \mathbf{x} - \mathbf{p}\parallel ,\mathbf{p} \in  {\mathcal{P}}^{1}}\right\}  . \tag{19}\]

2) L1-Exploration: The L1-exploration is defined as

\[{\mathbf{x}}^{ * } = \arg \mathop{\max }\limits_{{\mathbf{x} \in  {\mathcal{X}}^{1}}}\min \left\{  {\parallel \mathbf{x} - \mathbf{p}\parallel ,\mathbf{p} \in  {\mathcal{P}}^{1}}\right\}   \tag{20}\]

where \( {\mathcal{X}}^{1} \) includes the solution in new generated solution set \( \mathcal{X} \) whose predicted level by the KNN model is L1. In this article, \( \mathcal{X} \) is also generated by the above-described DE operators, and the number of levels is set to 5 .

For the models and infill criteria discussed above, we have the following remarks. Over the past two decades, a variety of regression and classification models have been employed to aid evolutionary algorithms in solving complex optimization problems. As far as our knowledge goes, GP, RBF, PRS, and KNN are the most effective and commonly used models in state-of-the-art SAEAs [9], [19], [44], each with unique advantages in handling diverse problems. In this article, our goal is to develop an SAEA with auto-configuration of models and infill criteria. Therefore, we have chosen these well-known models for their established strengths. However, it should be noted that other models and infill criteria can also be easily incorporated into our proposed algorithm. Moreover, regarding the parameters involved in the infill criteria, we have tested several representative values for each parameter in our preliminary experiments. The experimental results show that they do not have a significant influence. Therefore, we use commonly accepted settings for these parameters in this article.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 1118 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->

<!-- Media -->

<!-- figureText: Arm set Arm Arm set Arm High-level Low-level (b) Cluster (a) --><img src="data:image/jpeg;base64,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"/>

Fig. 1. Illustrative examples of HMAB clustering structures. (a) Disjoint clustering structure. (b) Hierarchical clustering structure.

<!-- Media -->

## C. Hierarchical Multiarmed Bandit

MAB is a powerful tool for online learning [37]. In the standard MAB problem,the player must pick an arm \( {a}_{t} \) from a set of arms \( \mathcal{A} \) at each time slot \( t \) and then obtain a reward \( {r}_{{a}_{t}} \) ,which is produced from a distribution that is unknown to the player. The performance of an algorithm in MAB is typically measured by the regret, and the player's goal is to minimize the expected cumulative regret over a sequence of \( T \) time slots. The expected cumulative regret can be expressed as follows [37]:

\[\mathbb{E}\left\lbrack  {R}_{T}\right\rbrack   = \mathop{\sum }\limits_{{t = 1}}^{T}{r}_{{a}_{t}^{ * }} - {r}_{{a}_{t}} \tag{21}\]

where \( {a}_{t}^{ * } \) denotes the unknown best arm at the \( t \) -th time slot.

Hierarchical MAB (HMAB) is an extension of the standard MAB. Two commonly used hierarchical structures have been developed [39].

1) Disjoint Clustering Structure: The \( K \) arms are classified into a set of clusters,and each arm \( a \in  \mathcal{A} \) belongs to only one cluster. The player first picks a cluster and then chooses an arm in the selected cluster. Finally, a reward is produced by the chosen arm. An illustrative example of disjoint clustering is shown in Fig. 1(a).

2) Hierarchical Clustering Structure: The arms can be divided into multiple levels. The arms on the same level are different from each other, and the different high-level arms can be associated with the same low-level arms. The player first selects an arm from the high-level and then chooses an arm from its associated low-level. Finally, a reward is returned by all chosen arms cooperatively. An illustrative example of hierarchical clustering is shown in Fig. 1(b).

Many theories and experiments have demonstrated that HMAB has significant advantages over standard MAB [37], [38], [39], [53], [54], [55]. In general, its benefits can be summarized into three aspects: 1) the regret bound of HMAB is lower than that of standard MAB; 2) HMAB can reduce the size of the arm space, especially when the arm space is large; and 3) HMAB can greatly mitigate the chances of selecting suboptimal arms, leading to improved performance in solving EOPs. Therefore, we propose integrating HMAB into SAEAs to enable the adaptive and cooperative selection of surrogate models and infill criteria. In this article, we consider the hierarchical clustering structure of HMAB, and its details will be explained in the following section.

<!-- Media -->

Algorithm 1: AutoSAEA \( \left( {N,\text{ MaxFEs },{\mathcal{A}}^{H},{\mathcal{A}}^{L},\alpha }\right) \) .

---

Input:
	Population size: \( N \)
	Maximum number of FEs: MaxFEs
	High-level arm set: \( {\mathcal{A}}^{H} = \left\{  {{a}_{1}^{H},\ldots ,{a}_{4}^{H}}\right\} \)
	Low-level arm set: \( {\mathcal{A}}^{L} = \left\{  {{a}_{1}^{L},\ldots ,{a}_{8}^{L}}\right\} \)
	Parameters: \( \alpha \)
Initialization:
	Use LHS to sample \( N \) solutions in the search space
	Evaluate and save them into the database \( \mathcal{D} \)
	Set \( {Q}_{a}^{H}\left( 1\right) ,a \in  {\mathcal{A}}^{H} \) and \( {Q}_{a}^{L},a \in  {\mathcal{A}}^{L} \) to 0
	Set \( {T}_{a}^{H}\left( 1\right) ,a \in  {\mathcal{A}}^{H} \) and \( {T}_{a}^{L},a \in  {\mathcal{A}}^{L} \) to 0
	Set \( \mathcal{C}\mathcal{A} \) based on \( {\mathcal{A}}^{H} \) and \( {\mathcal{A}}^{L} \)
	Set \( {FEs} = N \) and \( t = 1 \)
while \( {FEs} < {MaxFEs} \) do
	Select \( N \) best solutions from \( \mathcal{D} \) as the population \( \mathcal{P} \)
	if \( t \leq  \left| {\mathcal{C}\mathcal{A}}\right| \) then
		Select the \( t \) -th combinatorial arm in \( \mathcal{{CA}} \)
		Set \( {a}_{t}^{H} \) and \( {a}_{t}^{L} \) as the first and second arm in the
		selected combinatorial arm, respectively
	else
		Choose \( {a}_{t}^{H} \) and \( {a}_{t}^{L} \) by TL-UCB
	end if
	Obtain \( {\mathbf{x}}_{t} \) by \( {a}_{t}^{H} \) and \( {a}_{t}^{L} \) cooperatively
	Evaluate \( {\mathbf{x}}_{t} \)
	\( \mathcal{D} = \mathcal{D} \cup  {\mathbf{x}}_{t} \)
	\( {FEs} = {FEs} + 1 \)
	\( {T}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right)  = {T}_{{a}_{t}^{H}}^{H}\left( t\right)  + 1,{T}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right)  = {T}_{{a}_{t}^{L}}^{L}\left( t\right)  + 1 \)
	Update the value of \( {a}_{t}^{H} \) and \( {a}_{t}^{L} \) by TL-R
	\( t = t + 1 \)
end while
Output: The best solution in \( \mathcal{D} \)

---

<!-- Media -->

## IV. AUTOSAEA

## A. Algorithm Structure

The framework of the proposed AutoSAEA is shown in Algorithm 1. Its input includes the population size \( N \) (line 2), the maximum number of FEs MaxFEs (line 3), a set of high-level arms \( {\mathcal{A}}^{H} \) (line 4),a set of low-level arms \( {\mathcal{A}}^{L} \) (line 5),and the parameter \( \alpha \) (line 6). The high-level and low-level arms denote the surrogate model and the infill criterion, respectively. In this article,we set \( {\mathcal{A}}^{H} = \left\{  {{a}_{1}^{H} = \mathrm{{GP}},{a}_{2}^{H} = \mathrm{{RBF}},{a}_{3}^{H} = }\right. \) PRS, \( \left. {{a}_{4}^{H} = \mathrm{{KNN}}}\right\} \) and \( {\mathcal{A}}^{L} = \left\{  {{a}_{1}^{L} = \mathrm{{LCB}},{a}_{2}^{L} = \mathrm{{EI}},{a}_{3}^{L} = }\right. \) prescreening, \( {a}_{4}^{L} = \) local search, \( {a}_{5}^{L} = \) prescreening, \( {a}_{6}^{L} = \) local search, \( {a}_{7}^{L} = \mathrm{L}1 \) -exploitation, \( {a}_{8}^{L} = \mathrm{L}1 \) -exploration \( \} \) . \( {}^{1} \)

---

<!-- Footnote -->

\( {}^{1} \) Note that \( {a}_{3}^{L} \) and \( {a}_{4}^{L} \) are associated with RBF,and \( {a}_{5}^{L} \) and \( {a}_{6}^{L} \) are associated with PRS.

<!-- Footnote -->

---

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: XIE et al.: SAEA 1119-->

In the initialization (lines 7-13), an initial population with \( N \) solutions is generated by the Latin hypercube sampling (LHS) method (line 8). These solutions are evaluated directly and saved into a database \( \mathcal{D} \) (line 9). The value for each arm and the number of selections for each arm is set to 0 (lines 10 and 11). Moreover, to make each arm be selected at least once,a legal combinatorial arm set is set as \( \mathcal{C}\mathcal{A} \) \( = \{ \left( {\mathrm{{GP}},\mathrm{{LCB}}}\right) ,\left( {\mathrm{{GP}},\mathrm{{EI}}}\right) ,(\mathrm{{RBF}}, \) prescreening), \( (\mathrm{{RBF}}, \) local search), (PRS, prescreening), (PRS, local search), (KNN, L1- exploitation), (KNN, L1-exploration)\} (line 12). Finally, the current number of FEs and the iteration number are set to \( N \) and 1, respectively (line 13).

In the optimization process (lines 14-29), it first selects the \( N \) best solutions from \( \mathcal{D} \) as the population \( \mathcal{P} \) (line 15). If the number of iterations \( t \) is less than the cardinality of the combinatorial arm set \( \mathcal{{CA}} \) (line 16),the \( t \) -th combinatorial arm in \( \mathcal{{CA}} \) is chosen (line 17). Then,the high-level arm \( {a}_{t}^{H} \) and low-level arm \( {a}_{t}^{L} \) are set to the first and second arms in the selected combinatorial arm (line 18), respectively. Otherwise, they are determined by the TL-UCB method (line 20). When the high-level arm \( {a}_{t}^{H} \) and low-level arm \( {a}_{t}^{L} \) are selected,a new solution \( {\mathbf{x}}_{t} \) is determined by them cooperatively (line 22). To be specific:

1) when \( {a}_{t}^{H} \) represents the GP model,an offspring population with \( N \) solutions is first generated by DE operators. Then, the offspring solution with the best LCB value (if \( {a}_{t}^{L} = \mathrm{{LCB}} \) ) or best EI value (if \( {a}_{t}^{L} = \mathrm{{EI}} \) ) is chosen as \( {\mathbf{x}}_{t} \) ;

2) when \( {a}_{t}^{H} \) represents the RBF model or PRS model,if \( {a}_{t}^{L} = \) prescreening,an offspring population with \( N \) solutions is first generated by DE operators. Then, the offspring solution with the best \( {\widehat{f}}_{\mathrm{{RBF}}}\left( \mathbf{x}\right) \) value (if \( {a}_{t}^{H} = \mathrm{{RBF}} \) ) or \( {\widehat{f}}_{\mathrm{{PRS}}}\left( \mathbf{x}\right) \) value (if \( {a}_{t}^{H} = \) PRS) is chosen as \( {\mathbf{x}}_{t} \) . If \( {a}_{t}^{L} = \) local search,DE is used to optimize \( {\widehat{f}}_{\mathrm{{RBF}}}\left( \mathbf{x}\right) \) (if \( {a}_{t}^{H} = \mathrm{{RBF}} \) ) or \( {\widehat{f}}_{\mathrm{{PRS}}}\left( \mathbf{x}\right) \) (if \( {a}_{t}^{H} = \) PRS) to get \( {\mathbf{x}}_{t} \) . Note that in this article, the DE local search is conducted with a population size of \( N \) and a maximum number of \( {100D} + {1000} \) generations [25];

3) when \( {a}_{t}^{H} \) represents the KNN model,an offspring population with \( N \) solutions is first generated using DE operators. Then, \( {\mathbf{x}}_{t} \) is selected based on L1-exploitation (if \( {a}_{t}^{L} = \mathrm{L}1 \) -exploitation) or \( \mathrm{L}1 \) -exploration (if \( {a}_{t}^{L} = \) L1-exploration) from these offspring solutions.

Then, \( {\mathbf{x}}_{t} \) is evaluated and added into the database \( \mathcal{D} \) (lines 23 and 24), and the FEs is increased by 1 (line 25). Finally, the number of selections for \( {a}_{t}^{H} \) and \( {a}_{t}^{L} \) is increased by 1 (line 26), and their values are updated by the TL-R method (line 27).

When the maximum computational budget is consumed, the best solution in the database \( \mathcal{D} \) is output as the final solution (line 30). In the following, we will give a detailed introduction to the two core components, TL-UCB and TL-R, sequentially.

## B. TL-UCB

TL-UCB is the policy to select the high-level arm (surrogate model) and low-level arm (infill criterion). To choose a suitable high-level arm, the high-level UCB is executed, which is formulated as follows:

\[{a}_{t}^{H} = \underset{a \in  {\mathcal{A}}^{H}}{\arg \max }\left\lbrack  {{Q}_{a}^{H}\left( t\right)  + \sqrt{\frac{\alpha \ln \left( t\right) }{{T}_{a}^{H}\left( t\right) }}}\right\rbrack   \tag{22}\]

<!-- Media -->

Algorithm 2: \( \left( {{a}_{t}^{H},{a}_{t}^{L}}\right)  = \) TL-UCB \( \left( {{\mathcal{A}}^{H},{\mathcal{A}}^{L},t,{T}^{H},{T}^{L},{Q}^{H}\left( t\right) }\right. \) , \( {Q}^{L}\left( t\right) ,\alpha ) \) .

---

Input:
	High-level arms set: \( {\mathcal{A}}^{H} = \left\{  {{a}_{1}^{H},\ldots ,{a}_{4}^{H}}\right\} \)
	Low-level arm set: \( {\mathcal{A}}^{L} = \left\{  {{a}_{1}^{L},\ldots ,{a}_{8}^{L}}\right\} \)
	Current number of iterations: \( t \)
	Number of selection for each arm: \( {T}^{H}\left( t\right) \) and \( {T}^{L}\left( t\right) \)
	The value for each arm: \( {Q}^{H}\left( t\right) \) and \( {Q}^{L}\left( t\right) \)
	Parameter: \( \alpha \)
	Set \( {A}_{{a}_{1}^{H}}^{L} = \left\{  {{a}_{1}^{L},{a}_{2}^{L}}\right\}  ,{A}_{{a}_{2}^{H}}^{L} = \left\{  {{a}_{3}^{L},{a}_{4}^{L}}\right\} \) ,
			\( {A}_{{a}_{3}^{H}}^{{L}^{1}} = \left\{  {{a}_{5}^{L},{a}_{6}^{L}}\right\}  ,{A}_{{a}_{4}^{H}}^{{L}^{2}} = \left\{  {{a}_{7}^{L},{a}_{8}^{L}}\right\} \)
	\( {a}_{t}^{H} = \underset{a \in  {\mathcal{A}}^{H}}{\arg \max }\left\lbrack  {{Q}_{a}^{H}\left( t\right)  + \sqrt{\frac{\alpha \ln \left( t\right) }{{T}_{a}^{H}\left( t\right) }}}\right\rbrack \)
	\( {a}_{t}^{L} = \underset{a \in  {\mathcal{A}}_{{a}_{t}^{H}}^{L}}{\arg \max }\left\lbrack  {{Q}_{a}^{L}\left( t\right)  + \sqrt{\frac{\alpha \ln \left( t\right) }{{T}_{a}^{L}\left( t\right) }}}\right\rbrack \)
Output: High-level arm \( {a}_{t}^{H} \) and low-level arm \( {a}_{t}^{L} \)

---

<!-- figureText: High-level RBF Arm set PRS search Prescreening searcl xploitation Low-level LCB EI Prescreening --><img src="data:image/jpeg;base64,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"/>

Fig. 2. Association relationship between the high-level arms and low-level ones.

<!-- Media -->

where \( {Q}_{a}^{H}\left( t\right) \) denotes the value of the high-level arm \( a \) at the \( t \) -th iteration and \( {T}_{a}^{H}\left( t\right) \) is the number of selections of the high-level arm \( a \) during the past \( t - 1 \) iterations, \( t \) is the current iteration number,and \( \alpha \) is a control parameter used to balance the tradeoff between exploiting well-performing arms and exploring rarely selected arms.

When the high-level arm is determined, the low-level arm is picked up by the low-level UCB, which is defined as follows:

\[{a}_{t}^{L} = \underset{a \in  {\mathcal{A}}_{{a}_{t}^{H}}^{L}}{\arg \max }\left\lbrack  {{Q}_{a}^{L}\left( t\right)  + \sqrt{\frac{\alpha \ln \left( t\right) }{{T}_{a}^{L}\left( t\right) }}}\right\rbrack   \tag{23}\]

where \( {Q}_{a}^{L}\left( t\right) \) denotes the value of the low-level arm \( a \) in the \( t \) -th iteration, \( {T}_{a}^{L}\left( t\right) \) is the number of selections of the low-level arm \( a \) during the past \( t - 1 \) iterations. Here, it should be noted that not each high-level arm can be associated with all low-level ones. Therefore, \( {\mathcal{A}}_{{a}_{t}^{H}}^{L} \) denotes the low-level arms that can be associated with the high-level arm \( {a}_{t}^{H} \) . In this article,the association relationship between the high-level arms and low-level arms is shown in Fig. 2.

Moreover, the pseudo-code of the TL-UCB is provided in Algorithm 2.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 1120 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->

## C. TL-R

After determining the high-level arm (surrogate model) and low-level arm (infill criterion), the corresponding surrogate model is trained based on the current population \( \mathcal{P} \) . Then,a new solution \( {\mathbf{x}}_{t} \) is obtained by the selected high-level and low-level arms cooperatively. To measure the optimization utility of the chosen \( {a}_{t}^{L} \) and \( {a}_{t}^{H} \) ,a TL-R is designed as follows.

1) Low-Level Reward:

\[{r}_{{a}_{t}^{L}} =  - \frac{1}{N}I\left( {\mathbf{x}}_{t}\right)  + \frac{N + 1}{N} \tag{24}\]

where \( N \) is the population size and \( I\left( {\mathbf{x}}_{t}\right) \) denotes the ranking of \( {\mathbf{x}}_{t} \) in the current population \( \mathcal{P} \) . Specifically,if \( {\mathbf{x}}_{t} \) is the current best solution, \( I\left( {\mathbf{x}}_{t}\right)  = 1 \) such that \( {r}_{{a}_{t}^{L}} = 1 \) . On the contrary, if \( I\left( {\mathbf{x}}_{t}\right)  = N + 1 \) such that \( {r}_{{a}_{t}^{L}} = 0 \) . The low-level reward \( {r}_{{a}_{t}^{L}} \) has four characteristics: 1) \( {r}_{{a}_{t}^{L}} \) is bounded in \( \left\lbrack  {0,1}\right\rbrack  ;2){r}_{{a}_{t}^{L}} \) is linearly proportional to the ranking \( I\left( {\mathbf{x}}_{t}\right) \) of the newly generated solution \( {\mathbf{x}}_{t} \) ,the better the ranking,the larger the reward; 3) the low-level rewards are nonsparse; and 4) it remains stationary to some extent throughout the entire optimization process.

Based on the low-level reward \( {r}_{{a}_{t}^{L}} \) ,the value of \( {a}_{t}^{L} \) is updated as follows:

\[{Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right)  = \frac{{T}_{{a}_{t}^{L}}^{L}\left( t\right) {Q}_{{a}_{t}^{L}}^{L}\left( t\right)  + {r}_{{a}_{t}^{L}}}{{T}_{{a}_{t}^{L}}^{L}\left( t\right)  + 1}. \tag{25}\]

It should be noted that the value of the high-level arm \( {a}_{t}^{H} \) is increased if and only if the low-level reward is larger than its current value [e.g., \( {r}_{{a}_{t}^{L}} \geq  {Q}_{{a}_{t}^{L}}^{L}\left( t\right) \) ].

2) High-Level Reward: After the value \( {Q}_{{a}_{t}^{L}}^{L} \) of the low-level arm \( {a}_{t}^{L} \) is updated,the reward for the high-level arm \( {a}_{t}^{H} \) is designed as follows:

\[{r}_{{a}_{t}^{H}} = \frac{{Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right)  - {Q}_{{a}_{t}^{L}}^{L}\left( t\right) }{\left| {\mathcal{A}}_{{a}_{t}^{H}}^{L}\right| } \tag{26}\]

where \( {\mathcal{A}}_{{a}^{H}}^{L} \) denotes the low-level arm set that is associated with the high-level arm \( {a}_{t}^{H} \) . Clearly,the high-level reward \( {r}_{{a}_{t}^{H}} \) can be positive or negative. To be specific, if the value of the low-level arm \( {a}_{t}^{L} \) increases (e.g., \( {Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right)  - {Q}_{{a}_{t}^{L}}^{L}\left( t\right)  \geq  0 \) ), the reward of the high-level arm is positive. Otherwise, it is negative.

Based on the high-level reward \( {r}_{{a}_{t}^{H}} \) ,the value of \( {a}_{t}^{H} \) is updated as follows:

\[{Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right)  = {Q}_{{a}_{t}^{H}}^{H}\left( t\right)  + {r}_{{a}_{t}^{H}}. \tag{27}\]

Clearly,if the high-level reward of \( {a}_{t}^{H} \) is positive,its value will be increased. Otherwise, its value will be decreased. It should be pointed out that the value \( {Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right) \) of \( {a}_{t}^{H} \) is the mean of the value of its associated low-level arms. To be specific, putting (26) into (27), we have

\[{Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right)  = \frac{\left| {\mathcal{A}}_{{a}_{t}^{H}}^{L}\right|  \cdot  {Q}_{{a}_{t}^{H}}^{H}\left( t\right)  + {Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right)  - {Q}_{{a}_{t}^{L}}^{L}\left( t\right) }{\left| {\mathcal{A}}_{{a}_{t}^{H}}^{L}\right| }. \tag{28}\]

<!-- Media -->

<!-- figureText: Arm set Arm High-level Low-level --><img src="data:image/jpeg;base64,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"/>

Fig. 3. Numerical example of reward propagation in TL-R.

Algorithm 3: \( \left( {{Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right) ,{Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right) }\right)  = \operatorname{TL-R}\left( {N,\mathcal{P},{\mathbf{x}}_{t},{T}_{{a}_{t}^{L}}^{L}\left( t\right) }\right. \) , \( \left. {{Q}_{{a}_{t}^{L}}^{L}\left( t\right) ,{Q}_{{a}_{t}^{H}}^{H}\left( t\right) }\right) \) .

---

Input:
		Population size: \( N \)
		Population: \( \mathcal{P} \)
		New generated solution: \( {\mathbf{x}}_{t} \)
		Number of selections of \( {a}_{t}^{L} : {T}_{{a}_{t}^{L}}^{L}\left( t\right) \)
		The value of \( {a}_{t}^{L} \) and \( {a}_{t}^{H} : {Q}_{{a}_{t}^{L}}^{L}\left( t\right) \) and \( {Q}_{{a}_{t}^{H}}^{H}\left( t\right) \)
		Calculate the low-level reward \( {r}_{{a}_{t}^{L}} \) of \( {a}_{t}^{L} \) as (24)
		Calculate \( {Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right) \) of \( {a}_{t}^{L} \) as (25)
		Calculate the high-level reward \( {r}_{{a}_{t}^{H}} \) of \( {a}_{t}^{H} \) as (26)
	Calculate \( {Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right) \) of \( {a}_{t}^{H} \) as (27)
	Output: The updated values \( {Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right) \) and \( {Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right) \) of
\( {a}_{t}^{H} \) and \( {a}_{t}^{L} \)

---

<!-- Media -->

Assume that \( \left| {\mathcal{A}}_{{a}_{t}^{H}}^{L}\right|  \cdot  {Q}_{{a}_{t}^{H}}^{H}\left( t\right)  = \mathop{\sum }\limits_{{{a}^{L} \in  {\mathcal{A}}_{{a}_{t}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\left( t\right) \) holds for the \( t \) -th generation. Therefore

\[{Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right)  = \frac{\mathop{\sum }\limits_{{{a}^{L} \in  {\mathcal{A}}_{{a}_{t}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\left( t\right)  + {Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right)  - {Q}_{{a}_{t}^{L}}^{L}\left( t\right) }{\left| {\mathcal{A}}_{{a}_{t}^{H}}^{L}\right| }. \tag{29}\]

Since the value of the un-selected low-level arm is not updated, \( {Q}_{{a}^{L}}^{L}\left( {t + 1}\right)  = {Q}_{{a}^{L}}^{L}\left( t\right) ,\left( {{a}^{L} \neq  {a}_{t}^{L}}\right) \) . Finally,we have

\[{Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right)  = \frac{\mathop{\sum }\limits_{{{a}^{L} \in  {\mathcal{A}}_{{a}_{t}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\left( {t + 1}\right) }{\left| {\mathcal{A}}_{{a}_{t}^{H}}^{L}\right| }. \tag{30}\]

Since the values of both the low-level arm and high-level arm are initialized to 0,the equation \( \left| {\mathcal{A}}_{{a}_{t}^{H}}^{L}\right|  \cdot  {Q}_{{a}_{t}^{H}}^{H}\left( t\right)  = \) \( \mathop{\sum }\limits_{{{a}^{L} \in  {\mathcal{A}}_{{a}^{H}}^{L}}}{Q}_{{a}^{L}}^{L}\left( t\right) \) holds in the initialization (i.e., \( t = 1 \) ). Therefore,in each generation,the value of \( {a}_{t + 1}^{H} \) is exactly the mean of the values of its associated low-level arms.

The pseudocode of TL-R is provided in Algorithm 3. In addition, Fig. 3 illustrates a numerical example of reward propagation in TL-R. In this figure, we assume that at the \( t \) -th generation,TL-UCB selects the high-level arm \( {a}_{t}^{H} \) and low-level arm \( {a}_{t}^{L} \) ,and generates a solution \( {\mathbf{x}}_{t} \) by them cooperatively. Suppose the low-level reward is \( {r}_{{a}_{t}^{L}} = {0.8} \) ,as computed using (24), and the value of the low-level arm is \( {Q}_{{a}_{t}^{L}}^{L}\left( {t + 1}\right)  = {0.6} \) ,as obtained from (25). Then,using (26),we can compute the reward of the high-level arm \( {r}_{{a}_{t}^{H}} = {0.05} \) ,and finally,the value of the high-level arm \( {Q}_{{a}_{t}^{H}}^{H}\left( {t + 1}\right)  = {0.7} \) is determined using (27). The path of the reward is indicated by solid red lines.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: XIE et al.: SAEA 1121-->

Compared with other existing adaptive-model SAEAs [22], [23], [24], [25], the proposed AutoSAEA has the following differences.

1) Although the model and infill criterion configuration are common, they differ from those used in the existing adaptive-model SAEAs.

2) AutoSAEA can adaptively choose the surrogate model and infill criterion in an online manner. However, existing adaptive-model SAEAs are either only for model selection or infill criterion selection but do not study adaptive selection for both cooperatively.

3) AutoSAEA formulates the surrogate model and infill criterion selection as a TL-MAB. Moreover, a TL-R and a TL-UCB designed to effectively capture good coupling behavior between the surrogate model and infill criterion. The adopted online learning strategy of AutoSAEA is obviously different from those of the existing adaptive SAEAs.

## V. NUMERICAL EXPERIMENTS

## A. Benchmark Problems

Commonly used benchmark problems to evaluate the performance of the SAEAs are the Ellipsoid function, Rosenbrock function, Ackley function, Griewank function, Rastrigin function, and a few problems from CEC2005 competition [14], [15], [16], [18], [19], [32], [47], [56]. However, most of these problems are simple and hardly represent the complex characteristics of the real-world EOPs. Therefore, we first adopt CEC2005 [56] and CEC2015 problems [57] with \( D = {10} \) and \( D = {30} \) to evaluate the performance of our proposed AutoSAEA. Moreover, performance is measured using the function error metric \( f\left( {\mathbf{x}}_{ * }\right)  - f\left( {\mathbf{x}}^{o}\right) \) ,where \( {\mathbf{x}}_{ * } \) denotes the best solution found by the algorithm,and \( {\mathbf{x}}^{o} \) is the real optimum. To analyze the significant difference between the results obtained by all test algorithms, the Wilcoxon rank-sum test and Friedman test with the Hommel post-hoc procedure are conducted at a significant level of 0.05 [58], [59]. The symbols "+," "=," and "-" denote that AutoSAEA is statistically better than, competitive with, and worse than the compared algorithm, respectively.

## B. Comparison With Existing SAEAs

In this section, comparison experiments are conducted between AutoSAEA and two traditional Bayesian optimization methods (i.e., GP-LCB [43] and GP-EI [40]) and eight state-of-the-art SAEAs (i.e., IKAEA (2021) [42], ESAO (2019) [32], CA-LLSO (2020) [47], TS-DDEO (2021) [31], SA-MPSO (2021) [16], SAMFEO (2022) [58], GL-SADE (2022) [18], and ESA (2022) [25]). The parameters of all compared algorithms remain the same as in their original papers. For the proposed AutoSAEA,parameters \( N \) and \( \alpha \) are set to 100 and 2.5, respectively. The statistical comparison results are obtained under 1000 FEs and 20 independent runs [15], [16], [18], [32], [47] on each problem. The mean and standard deviation of the obtained function error values for each algorithm on the CEC2005 10D problems, CEC2005 30D problems, CEC2015 10D problems, and CEC2015 30D problems are provided in Tables I-IV of the supplementary file, respectively, and the statistical results are shown in Table I.

For the CEC2005 10D problems, AutoSAEA exhibits significant advantages over all compared algorithms. Specifically, it outperforms GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 8, 9,12,13,10,14,11,11,14,and 9 out of 15 problems,respectively. Moreover, it is only worse than CA-LLSO on 1 out of 15 problems. Based on the Friedman test, AutoSAEA ranks first and significantly outperforms all compared algorithms except GP-LCB and ESA.

For the CEC2005 30D problems, AutoSAEA is significantly better than all compared algorithms in most cases. It outperforms GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 7, 8, 12,11,10,9,12,8,12,and 8 out of 15 problems,respectively. Moreover, it is only worse than GP-LCB, GP-EI, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on2,2,1,1,2,1,4,2,and 3 out of 15 problems. AutoSAEA takes first place and is significantly better than all compared algorithms except GP-LCB, GP-EI, SAMFEO, and ESA according to the Friedman test results.

For the CEC2015 10D problems, AutoSAEA achieves the best results in most cases. Specifically, it is significantly better than or at least as good as TS-DDEO and ESAO in all cases. Additionally, it is only worse than GP-LCB, GP-EI, IKAEA, CA-LLSO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 2, 2,1,1,1,2,2,and 2 out of 15 problems,respectively. Based on the statistical results of the Friedman test, AutoSAEA is the champion and significantly outperforms all compared SAEAs except GP-LCB, GP-EI, SAMFEO, and ESA.

For the CEC2015 30D problems, AutoSAEA is still the winner in the overall comparison of algorithms. It is significantly better than GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 5, 6,12,12,9,9,12,6,12,and 6 out of 15 cases,respectively. Moreover, it is only worse than GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 2, 3, 1, 0, 3, 2, 1, 1, 1, and 1 out of 15 problems, respectively. According to the statistical results of the Friedman test, AutoSAEA ranks first and is significantly better than IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, and GL-SADE.

To evaluate the performance of SAEAs, it is necessary to test them on various problems with different characteristics. It is well known that different models and infill criteria are suitable for solving different problems [23], [24], [25], [30]. The proposed AutoSAEA does not focus on developing new models or infill criteria but aims to leverage the existing well-established ones to improve the performance of SAEA. Therefore, although the surrogate model and infill criterion adopted in AutoSAEA are common configurations, the reason why it performs better than other state-of-the-art SAEAs is explained as follows.

1) Compared With Single-Model SAEAs: Single-model SAEAs use a fixed surrogate model and infill criterion for all problems. While these algorithms can solve specific problems well, they often perform poorly on others. Therefore, when evaluating their performance on a sufficiently diverse set of problems, single-model SAEAs tend to perform worse than multiple-model SAEAs and adaptive-model SAEAs [18], [19], [35].

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 1122 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->

<!-- Media -->

TABLE I

COMPARISON RESULTS BETWEEN AUTOSAEA AND ITS COMPETITORS AND THE RANKING OF EACH ALGORITHM ON CEC2005 PROBLEMS AND CEC2015 PROBLEMS WITH \( D = {10} \) AND \( D = {30} \)

<table><tr><td colspan="12">CEC2005 10D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>8/7/0</td><td>9/6/0</td><td>\( {12}/3/0 \)</td><td>13/2/0</td><td>10/4/1</td><td>14/1/0</td><td>11/4/0</td><td>11/4/0</td><td>14/1/0</td><td>9/6/0</td><td>NA</td></tr><tr><td>Ranking</td><td>4.2</td><td>5.0</td><td>7.3</td><td>8.7</td><td>6.3</td><td>7.2</td><td>6.7</td><td>5.9</td><td>7.8</td><td>4.1</td><td>1.9</td></tr><tr><td>\( p \) -value</td><td>0.12</td><td>0.03</td><td>0.0001</td><td>0</td><td>0.001</td><td>0.0001</td><td>0.0004</td><td>0.004</td><td>0</td><td>0.12</td><td>NA</td></tr><tr><td colspan="12">CEC2005 30D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>7/6/2</td><td>8/5/2</td><td>12/3/0</td><td>11/3/1</td><td>10/4/1</td><td>9/4/2</td><td>12/2/1</td><td>8/3/4</td><td>12/1/2</td><td>8/4/3</td><td>NA</td></tr><tr><td>Ranking</td><td>4.7</td><td>4.6</td><td>8.8</td><td>8.0</td><td>6.7</td><td>6.1</td><td>7.5</td><td>4.1</td><td>7.8</td><td>5.0</td><td>2.7</td></tr><tr><td>\( p \) -value</td><td>0.30</td><td>0.30</td><td>0</td><td>0.0001</td><td>0.005</td><td>0.03</td><td>0.0004</td><td>0.30</td><td>0.0002</td><td>0.22</td><td>NA</td></tr><tr><td colspan="12">CEC2015 10D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>7/6/2</td><td>7/6/2</td><td>11/3/1</td><td>14/1/0</td><td>12/2/1</td><td>11/4/0</td><td>12/2/1</td><td>11/2/2</td><td>12/1/2</td><td>10/3/2</td><td>NA</td></tr><tr><td>Ranking</td><td>4.5</td><td>4.1</td><td>6.3</td><td>9.1</td><td>7.7</td><td>7.7</td><td>5.9</td><td>5.0</td><td>7.7</td><td>5.1</td><td>2.6</td></tr><tr><td>\( p \) -value</td><td>0.23</td><td>0.23</td><td>0.01</td><td>0</td><td>0.0002</td><td>0.002</td><td>0.03</td><td>0.18</td><td>0.0002</td><td>0.18</td><td>NA</td></tr><tr><td colspan="12">CEC2015 30D problems</td></tr><tr><td/><td>GP-LCB</td><td>GP-EI</td><td>IKAEA</td><td>ESAO</td><td>CA-LLSO</td><td>TS-DDEO</td><td>SA-MPSO</td><td>SAMFEO</td><td>GL-SADE</td><td>ESA</td><td>AutoSAEA</td></tr><tr><td>+/=/-</td><td>5/8/2</td><td>6/6/3</td><td>12/2/1</td><td>12/3/0</td><td>9/3/3</td><td>9/4/2</td><td>12/2/1</td><td>6/8/1</td><td>12/2/1</td><td>6/8/1</td><td>NA</td></tr><tr><td>Ranking</td><td>3.4</td><td>4.1</td><td>8.4</td><td>8.3</td><td>7.0</td><td>5.9</td><td>7.4</td><td>5.2</td><td>9.2</td><td>4.3</td><td>2.9</td></tr><tr><td>\( p \) -value</td><td>0.74</td><td>0.74</td><td>0.0001</td><td>0.0001</td><td>0.004</td><td>0.05</td><td>0.001</td><td>0.26</td><td>0</td><td>0.74</td><td>NA</td></tr></table>

<!-- Media -->

2) Compared With Multiple-Model SAEAs: While multiple-model SAEAs can leverage the advantages of different models and infill criteria, they cannot identify which ones are most suitable for a given problem. This often leads to high consumption of computational resources on ineffective models and infill criteria [23].

3) Compared With Adaptive-Model SAEAs: Adaptive-model SAEAs aim to not only exploit the advantages of different models and infill criteria but also identify the most suitable ones for a given problem. However, the performance of adaptive SAEAs depends on the specific model and infill criterion configuration used, as well as the adaptive selection strategy. Compared to existing adaptive-model SAEAs, AutoSAEA not only enriches the model/infill criterion configuration but also adopts a more effective adaptive selection strategy (i.e., TL-MAB) to select them in a hierarchical, coupled way. Therefore, AutoSAEA can outperform existing state-of-the-art adaptive-model SAEAs.

Due to page limitations, we have included the convergence profiles of all tested algorithms, as well as the computational complexity of AutoSAEA and running times of all compared algorithms, in the supplementary file.

## C. Adaptive Behavior Analysis

AutoSAEA formulates the surrogate model and infill criterion selection as a TL-MAB problem and designs a TL-R and a TL-UCB to address it. To demonstrate the effectiveness of the adaptive surrogate model and infill criterion selection used in AutoSAEA, we first compare it with eleven variants.

1) V-CA1 to V-CA8: AutoSAEA uses the fixed combinatorial arms in \( \mathcal{{CA}} \) during the entire optimization process. V-CA \( i \) denotes that it uses the \( i \) th \( \left( {i = 1,\ldots ,8}\right) \) combinatorial arm.

2) V-Random: The surrogate model and infill criterion are randomly selected.

3) V-SLUCB: AutoSAEA uses a single-level UCB and a single-level reward to adaptively choose the combinatorial arms from \( \mathcal{{CA}} \) .

4) \( V - Q \) : AutoSAEA uses the \( Q \) -learning method [25] to adaptively select the combinatorial arms from \( \mathcal{{CA}} \) in each iteration.

The experiments are conducted on the CEC2005 10D and 30D problems, with all experimental setups kept the same as in the previous section. The mean and standard deviation of the function error value for each variant are provided in Tables V and VI of the supplementary file, and the statistical results are shown in Table II.

For the CEC2005 10D problems, the Wilcoxon rank-sum test indicates that AutoSAEA significantly outperforms its variants. Specifically, it outperforms or competes with all variants on all problems, except that it is worse than VCA-5 and V-Q on one problem each out of 15. Overall, AutoSAEA achieves the best ranking and significantly outperforms VCA- 2, VCA-4, VCA-5, VCA-6, VCA-7, and VCA-8 according to the Friedman test.

For the CEC2005 30D problems, based on the Wilcoxon rank-sum test, AutoSAEA is significantly better than all variants in most cases, and it is only outperformed by V-CA1, V-CA2, VCA-5, V-CA7, V-Random, and V-Q on 2, 2, 2, 2, 2, and 2 out of 15 problems, respectively.

While AutoSAEA exhibits remarkable advantages over some of its variants, such as V-CA4, V-CA5, V-CA6, V-CA7, and V-CA8, it is worth noting that there is no significant difference between AutoSAEA and some of its variants, namely, V-CA1, V-CA2, V-CA3, V-Random, V-SLUCB, and V-Q. This indicates that AutoSAEA does not have advantages over its variants on some problems. The reasons behind this are provided as follows, based on the detailed analysis of the results.

1) For some problems,such as CEC2005 F8 \( \left( {D = {10}}\right) \) , CEC2005 F15 \( \left( {D = {10}}\right) \) ,CEC2005 F8 \( \left( {D = {30}}\right) \) , CEC2005 F11 \( \left( {D = {30}}\right) \) ,and CEC2005 F14 \( \left( {D = {30}}\right) \) , no models and infill criteria work effectively, so AutoSAEA cannot address them well.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: XIE et al.: SAEA 1123-->

<!-- Media -->

TABLE II

COMPARISON RESULTS BETWEEN AUTOSAEA AND ITS VARIANTS AND THEIR RANKING ON CEC2005 10D AND 30D PROBLEMS

<table><tr><td colspan="13">CEC2005 10D problems</td></tr><tr><td/><td>V-CA1</td><td>V-CA2</td><td>V-CA3</td><td>V-CA4</td><td>V-CA5</td><td>V-CA6</td><td>V-CA7</td><td>V-CA8</td><td>V-Random</td><td>V-SLUCB</td><td>V-Q</td><td>AutoSAEA</td></tr><tr><td>\( + / = 1 - \)</td><td>8/7/0</td><td>9/6/0</td><td>8/7/0</td><td>14/1/0</td><td>\( {13}/1/1 \)</td><td>\( {14}/1/0 \)</td><td>13/2/0</td><td>\( {14}/1/0 \)</td><td>8/7/0</td><td>2/13/0</td><td>11/3/1</td><td>NA</td></tr><tr><td>Ranking</td><td>5.4</td><td>6.6</td><td>5.3</td><td>9.1</td><td>7.3</td><td>8.9</td><td>9.5</td><td>10.9</td><td>4.0</td><td>3.1</td><td>5.1</td><td>2.5</td></tr><tr><td>\( p \) -value</td><td>0.13</td><td>0.01</td><td>0.13</td><td>0</td><td>0.002</td><td>0</td><td>0</td><td>0</td><td>0.47</td><td>0.61</td><td>0.13</td><td>NA</td></tr><tr><td colspan="13">CEC2005 30D problems</td></tr><tr><td/><td>V-CA1</td><td>V-CA2</td><td>V-CA3</td><td>V-CA4</td><td>V-CA5</td><td>V-CA6</td><td>V-CA7</td><td>V-CA8</td><td>V-Random</td><td>V-SLUCB</td><td>V-O</td><td>AutoSAEA</td></tr><tr><td>\( + / = / - \)</td><td>7/6/2</td><td>8/5/2</td><td>8/7/0</td><td>11/4/0</td><td>11/2/2</td><td>\( {13}/2/0 \)</td><td>11/2/2</td><td>13/2/0</td><td>9/4/2</td><td>4/11/0</td><td>9/4/2</td><td>NA</td></tr><tr><td>Ranking</td><td>4.2</td><td>4.9</td><td>5.5</td><td>8.5</td><td>8.5</td><td>11.3</td><td>8.2</td><td>10.7</td><td>4.8</td><td>3.5</td><td>6.1</td><td>2.6</td></tr><tr><td>\( p \) -value</td><td>0.41</td><td>0.38</td><td>0.18</td><td>0</td><td>0</td><td>0</td><td>0.0001</td><td>0</td><td>0.38</td><td>0.51</td><td>0.003</td><td>NA</td></tr></table>

<!-- Media -->

2) For some problems,such as CEC2005 F9 \( \left( {D = {10}}\right) \) , CEC2005 F10 \( \left( {D = {10}}\right) , \) CEC2005 F12 \( \left( {D = {10}}\right) \) , CEC2005 F2 \( \left( {D = {30}}\right) \) ,and CEC2005 F3 \( \left( {D = {30}}\right) \) , they can be addressed well by various models and infill criteria, so AutoSAEA does not have significant advantages over its some variants on these problems.

To visually demonstrate the adaptive behavior of our proposed AutoSAEA, we show the selected high-level arm (surrogate model) and low-level arm (infill criterion) during the optimization process of AutoSAEA in solving four representative problems,namely,CEC2005 F4 \( \left( {D = {10}}\right) \) ,CEC2005 F10 \( \left( {D = {10}}\right) ,\mathrm{{CEC}}{2005}\mathrm{\;F}2\left( {D = {30}}\right) \) ,and \( \mathrm{{CEC}}{2005}\mathrm{\;F}{12} \) \( \left( {D = {30}}\right) \) ,in Fig. 4. Additionally,we count the number of times each surrogate model and infill criterion is chosen in Fig. 5. From Figs. 4 and 5, we observe the following.

1) For CEC2005 F4 \( \left( {D = {10}}\right) \) ,the PRS with prescreen-ing is selected most frequently. This is because this problem is unimodal, its landscape is smooth, and its dimensionality is small, making PRS a good choice for approximating this problem.

2) For CEC2005 F10 \( \left( {D = {10}}\right) \) ,the algorithm prefers different models at different optimization stages. Specifically, the whole optimization procedure can be roughly divided into five stages, and GP, KNN, RBF, PRS, and KNN are, respectively, selected most frequently.

3) For CEC2005 F2 \( \left( {D = {30}}\right) \) ,the GP model with EI and LCB is selected most frequently, especially in later stages. Interestingly, GP always combines LCB when \( {650} < \) FEs \( < {750} \) .

4) For CEC2005 F12 (D = 30),GP with LCB or EI is mainly chosen when \( {100} < {FEs} < {350} \) . However,when \( {350} < \) FEs \( < {550} \) ,the RBF model with prescreening or local search is preferred, and the GP model is no longer selected. When \( {550} < {FEs} < {1000} \) ,RBF and GP are the most and second most commonly chosen models, respectively.

In summary, for different problems and different optimization stages, the selected surrogate model and infill criterion are different. Therefore, combined with the outstanding performance, the effectiveness of the surrogate model and infill criterion auto-configuration in AutoSAEA has been verified.

### D.Oil Reservoir Production Optimization Problem

AutoSAEA is applied to an oil reservoir production problem in the section to demonstrate its advantage further. This production optimization problem aims to find the optimal control parameters for the water-injection-rate of injection wells and fluid-production-rate of production wells in the expected life to maximize the net present value (NPV). In general, the oil reservoir production optimization problem can be expressed in the following form [60]:

\[\max \mathop{\sum }\limits_{{t = 1}}^{T}{\Delta }_{t}{r}_{o}{Q}_{o,t}\left( \mathbf{x}\right)  - {r}_{w}{Q}_{w,t}\left( \mathbf{x}\right)  - {r}_{i}{Q}_{i,t}\left( \mathbf{x}\right) \]

\[\text{s.t.}0 \leq  {x}_{i,t} \leq  {500},i = 1,\ldots ,8,t = 1,\ldots ,5 \tag{31}\]

<!-- Media -->

TABLE III

RESULT OF GP-LCB, GP-EI, IKAEA, ESAO, TS-DDEO, SA-MPSO, GL-SADE, ESA, AND AUTOSAEA ON THE OIL RESERVOIR Production Optimization Problem. The Unit of Time Is Seconds

<table><tr><td>Algorithm</td><td>Mean(std)</td><td>Median</td><td>Worst</td><td>Best</td><td>Time</td></tr><tr><td>GP-LCB</td><td>\( {1.34}\mathrm{e} + {04}\left( {{2.2}\mathrm{e} + {02}}\right)  = \)</td><td>1.35e+04</td><td>\( {1.31e} + {00} \)</td><td>1.36e+04</td><td>\( {2.58e} + {05} \)</td></tr><tr><td>GP-EI</td><td>\( {1.34}\mathrm{e} + {04}\left( {{1.7}\mathrm{e} + {02}}\right)  = \)</td><td>\( {1.35}\mathrm{e} + {04} \)</td><td>1.32e+04</td><td>1.36e+04</td><td>\( {2.18e} + {05} \)</td></tr><tr><td>IKAEA</td><td>\( {1.28}\mathrm{e} + {04}\left( {{1.9}\mathrm{e} + {02}}\right)  + \)</td><td>\( {1.27}\mathrm{e} + {04} \)</td><td>1.26e+04</td><td>\( {1.31}\mathrm{e} + {04} \)</td><td>\( {2.45e} + {05} \)</td></tr><tr><td>ESAO</td><td>1.27e+04(6.4e+02)+</td><td>\( {1.30}\mathrm{e} + {04} \)</td><td>\( {1.16e} + {04} \)</td><td>1.32e+04</td><td>\( {2.64}\mathrm{e} + {05} \)</td></tr><tr><td>TS-DDEO</td><td>\( {1.34}\mathrm{e} + {04}\left( {{1.7}\mathrm{e} + {02}}\right)  = \)</td><td>1.34e+04</td><td>1.31e+04</td><td>1.36e+04</td><td>\( {3.04}\mathrm{e} + {05} \)</td></tr><tr><td>SA-MPSO</td><td>1.13e+04(1.3e+02)+</td><td>\( {1.13}\mathrm{e} + {04} \)</td><td>\( {1.12}\mathrm{e} + {04} \)</td><td>\( {1.15}\mathrm{e} + {04} \)</td><td>\( {3.52}\mathrm{e} + {05} \)</td></tr><tr><td>GL-SADE</td><td>\( {1.31e} + {04}\left( {{3.8e} + {02}}\right)  = \)</td><td>\( {1.33}\mathrm{e} + {04} \)</td><td>\( {1.28e} + {04} \)</td><td>\( {1.35}\mathrm{e} + {04} \)</td><td>\( {3.48}\mathrm{e} + {05} \)</td></tr><tr><td>ESA</td><td>\( {1.34}\mathrm{e} + {04}\left( {{1.7}\mathrm{e} + {02}}\right)  = \)</td><td>\( {1.33}\mathrm{e} + {04} \)</td><td>1.33e+04</td><td>1.37e+04</td><td>\( {2.98}\mathrm{e} + {05} \)</td></tr><tr><td>AutoSAEA</td><td>1.36e+04(3.0e+02)</td><td>\( {1.35}\mathrm{e} + {04} \)</td><td>1.32e+04</td><td>1.41e+04</td><td>\( {2.13}\mathrm{e} + {05} \)</td></tr></table>

<!-- Media -->

where \( \mathbf{x} = {\left( {x}_{1,1},\ldots ,{x}_{1,5},\ldots ,{x}_{8,1},\ldots ,{x}_{8,5}\right) }^{\top } \) is the decision vector,and \( {x}_{i,t} \) denotes the flow rate of the \( i \) th well at the \( t \) -th time step. Therefore,40 variables need to be optimized. The lower and upper bounds of each decision variable are set to \( 0\mathrm{{STB}}/ \) day and \( {500}\mathrm{{STB}}/ \) day,respectively. \( T = 5 \) denotes the number of time steps. \( {\Delta }_{t} = {720} \) is the time of the \( t \) -th time step. \( {r}_{o},{r}_{w} \) ,and \( {r}_{i} \) ,respectively,denote the oil-production revenue, water-production cost, and water-injection cost, which are set to 20 USD/STB, 1 USD/STB, and 3 USD/STB,respectively. \( {Q}_{o,t},{Q}_{w,t} \) ,and \( {Q}_{i,t} \) denote the oil-production rate, water-production rate, and injection-flow-rate at the \( t \) -th time step,respectively,which are calculated by a numerical simulator. In this case, the Egg model [61], including eight water-injection wells and four production wells, is selected as the reservoir simulator. More information about the Egg model can be found in [61]. In this article, the MRST toolbox [62] is adopted to conduct the Egg model. Note that it takes about \( {40}\mathrm{\;s} \) to evaluate one solution using the simulator.

To demonstrate the performance of AutoSAEA in solving the oil reservoir production optimization problem, eight existing SAEAs are used to test. To make a fair comparison, the initial number of solutions and the total number of FEs for all algorithms are set to 100 and 1000, respectively. Moreover, each algorithm conducts five independent runs.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 1124 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->

<!-- Media -->

<!-- figureText: F4 \( \left( {D = {10}}\right) \) \( \mathrm{F}{10}\left( {D = {10}}\right) \) GP RBF KNN Prescreening (RBF) ( "2000 ) (   ) (   ) (   ) (   ) (   ) (   ) ) (   ) ) (   ) ) Local search (RBF) CIRITIONITION Prescreening (PRS) Local search (PRS) L1-exploration 0 100 200 300 400 500 600 800 900 1000 FEs F \( {12}\left( {D = {30}}\right) \) GP RBF PRS KNN O O O O O O O O O O O O O O O O O LCB _____ Prescreening (RBF) CONTITION CONDICE Local search (RBF) ④ ② C D Prescreening (PRS) 课文： O O O O Local search (PRS) ③O O O O L1-exploitation GITO CO COMO COM O O O O O O O O O O O O O O O O O 300 400 500 900 100 RBF 60 High-level Prescreening (RBF) CO Local search (RBF) Prescreening (PRS) Local search (PRS) L1-exploration - D \{ 100 200 300 400 500 600 700 800 900 1000 \( \mathrm{F}2\left( {D = {30}}\right) \) GP PRS High-leve KNN ORMO NOMO O O O O O O O O O O O O O O O O LCB Prescreening (RBF ORIDOOOOOOOO 00000 Local search (RBF) OO O O O Prescreening (PRS) Local search (PRS) ③ ① ② ① ② (   ) L1-exploitation 202012年11月11日(2000年) 0 100 400 500 600 1000 --><img src="data:image/jpeg;base64,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"/>

Fig. 4. Selected surrogate model and infill criterion during the optimization process of AutoSAEA on CEC2005 F4 \( \left( {D = {10}}\right) \) ,CEC2005 F10 \( \left( {D = {10}}\right) \) , CEC2005 F2 \( \left( {D = {30}}\right) \) ,and CEC2005 F12 \( \left( {D = {30}}\right) \) .

<!-- figureText: 700 - GP 2 RBF 3 PRS DIKNN 3LCB -1.E1 Prescreening (RBF) & Local search (RBF) Prescreening (PRS) - L1-exploitation L1-exploration CEC2005 F2 (30D) CEC2005 F12 (30D) 600 500 200 145 100 3535 CEC2005 F4 (10D) CEC2005 F10 (10D) --><img src="data:image/jpeg;base64,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"/>

Fig. 5. Number of times each surrogate model and infill criterion is chosen during the optimization process of AutoSAEA on CEC2005 F4 ( \( D = {10} \) ), CEC2005 F10 \( \left( {D = {10}}\right) \) ,CEC2005 F2 \( \left( {D = {30}}\right) \) ,and CEC2005 F12 \( \left( {D = {30}}\right) \) .

<!-- figureText: 14000 GP-EI IKAEA ESAO TS-DDEO SA-MPSO ESA AutoSAEA 600 800 1000 FEs 12000 10000 NPV 8000 6000 2000 0 200 400 --><img src="data:image/jpeg;base64,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"/>

Fig. 6. Average convergence profile of the median NPV value and its interquartile ranges of GP-LCB, GP-EI, IKAEA, ESAO, TS-DDEO, SA-MPSO, GL-SADE, ESA, and AutoSAEA on the oil reservoir production optimization problem.

<!-- Media -->

The statistical results are provided in Table III. It can be seen that AutoSAEA can get the best average performance. Based on the Wilcoxon rank-sum test, AutoSAEA is significantly better than IKAEA, ESAO, and SA-MPSO and performs competitively with GP-LCB, GP-EI, ESA, TS-DDEO, and GL-SADE. Moreover, the running time of AutoSAEA is also the lowest, which shows the better computational efficiency of AutoSAEA compared with its competitors. Besides, the average convergence performance of each algorithm on this problem is plotted in Fig. 6, from which we can find that AutoSAEA also has some advantages in solving this oil reservoir production optimization problem.

## VI. CONCLUSION

The surrogate model and infill criterion are vital to the performance of SAEAs. To enhance the ability of SAEA to solve various EOPs, this article proposes a TL-MAB to cooperatively choose the surrogate model and infill criterion in an online manner. To achieve this, a TL-R mechanism is defined to measure the optimization utility of the surrogate model and infill criterion in a hierarchical and coupled manner. Additionally, a TL-UCB strategy is designed to choose them cooperatively and adaptively. With these adaptive selection components, an auto SAEA has been proposed, called AutoSAEA. The performance of AutoSAEA has been demonstrated by comparing it with several state-of-the-art SAEAs on two sets of benchmark problems (CEC2005 and CEC2015) and a real-world oil reservoir production optimization problem. Furthermore, the adaptive behavior of AutoSAEA has been numerically verified, and the sensitivity of its control parameters has been analyzed.

In the future, we plan to extend the adaptive surrogate model and infill criterion selection method to expensive multiobjective optimization. Moreover, we will use the proposed AutoSAEA to tackle other real-world EOPs. REFERENCES

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: XIE et al.: SAEA 1125-->

[1] Y. Lian, A. Oyama, and M.-S. Liou, "Progress in design optimization using evolutionary algorithms for aerodynamic problems," Progr. Aerosp. Sci., vol. 46, nos. 5-6, pp. 199-223, 2010.

[2] T. W. Simpson, A. J. Booker, D. Ghosh, A. A. Giunta, P. N. Koch, and R.-J. Yang, "Approximation methods in multidisciplinary analysis and optimization: A panel discussion," Struct. Multidiscipl. Optim., vol. 27, no. 5, pp. 302-313, 2004.

[3] Q. Chen, G. Li, Q. Zhang, Q. Tang, and G. Zhang, "Optimal design of passive control of space tethered-net capture system," IEEE Access, vol. 7, pp. 131383-131394, 2019.

[4] Z. Wang, Y.-S. Ong, J. Sun, A. Gupta, and Q. Zhang, "A generator for multiobjective test problems with difficult-to-approximate Pareto front boundaries," IEEE Trans. Evol. Comput., vol. 23, no. 4, pp. 556-571, Aug. 2019.

[5] W. Gao, Z. Wei, M. Gong, and G. G. Yen, "Solving expensive multimodal optimization problem by a decomposition differential evolution algorithm," IEEE Trans. Cybern., vol. 53, no. 4, pp. 2236-2246, Apr. 2023.

[6] G. Li and Q. Zhang, "Multiple penalties and multiple local surrogates for expensive constrained optimization," IEEE Trans. Evol. Comput., vol. 25, no. 4, pp. 769-778, Aug. 2021.

[7] G. Li, L. Xie, Z. Wang, H. Wang, and M. Gong, "Evolutionary algorithm with individual-distribution search strategy and regression-classification surrogates for expensive optimization," Inf. Sci., vol. 634, pp. 423-442, Jul. 2023.

[8] H. Wang, H. Xu, and Z. Zhang, "High-dimensional multi-objective Bayesian optimization with block coordinate updates: Case studies in intelligent transportation system," IEEE Trans. Intell. Transp. Syst., early access, Feb. 7, 2023, doi: 10.1109/TITS.2023.3241069.

[9] T. Goel, R. T. Hafkta, and W. Shyy, "Comparing error estimation measures for polynomial and kriging approximation of noise-free functions," Struct. Multidiscip. Optim., vol. 38, no. 5, pp. 429-442, 2009.

[10] L. M. Zouhal and T. Denoeux, "An evidence-theoretic k-NN rule with parameter optimization," IEEE Trans. Syst., Man, Cybern. C, Appl. Rev., vol. 28, no. 2, pp. 263-271, Mar. 1998.

[11] S. M. Clarke, J. H. Griebsch, and T. W. Simpson, "Analysis of support vector regression for approximation of complex engineering analyses," \( J \) . Mech. Design, vol. 127, no. 6, pp. 1077-1087, 2005.

[12] Y. Jin, M. Olhofer, and B. Sendhoff, "A framework for evolutionary optimization with approximate fitness functions," IEEE Trans. Evol. Comput., vol. 6, no. 5, pp. 481-494, Oct. 2002.

[13] B. Liu, Q. Zhang, and G. G. E. Gielen, "A Gaussian process surrogate model assisted evolutionary algorithm for medium scale expensive optimization problems," IEEE Trans. Evol. Comput., vol. 18, no. 2, pp. 180-192, Apr. 2014.

[14] J. Tian, Y. Tan, J. Zeng, C. Sun, and Y. Jin, "Multiobjective infill criterion driven Gaussian process-assisted particle swarm optimization of high-dimensional expensive problems," IEEE Trans. Evol. Comput., vol. 23, no. 3, pp. 459-472, Jul. 2019.

[15] F. Li, X. Cai, L. Gao, and W. Shen, "A surrogate-assisted multiswarm optimization algorithm for high-dimensional computationally expensive problems," IEEE Trans. Cybern., vol. 51, no. 3, pp. 1390-1402, Mar. 2021.

[16] Y. Liu, J. Liu, and Y. Jin, "Surrogate-assisted multipopulation particle swarm optimizer for high-dimensional expensive optimization," IEEE Trans. Syst., Man, Cybern., Syst., vol. 52, no. 7, pp. 4671-4684, Jul. 2022.

[17] J. Liu, Y. Wang, G. Sun, and T. Pang, "Multisurrogate-assisted ant colony optimization for expensive optimization problems with continuous and categorical variables," IEEE Trans. Cybern., vol. 52, no. 11, pp. 11348-11361, Nov. 2022.

[18] W. Wang, H.-L. Liu, and K. C. Tan, "A surrogate-assisted differential evolution algorithm for high-dimensional expensive optimization problems," IEEE Trans. Cybern., vol. 53, no. 4, pp. 2685-2697, Apr. 2023.

[19] H. Wang, Y. Jin, and J. Doherty, "Committee-based active learning for surrogate-assisted particle swarm optimization of expensive problems," IEEE Trans. Cybern., vol. 47, no. 9, pp. 2664-2677, Sep. 2017.

[20] T. Sonoda and M. Nakata, "Multiple classifiers-assisted evolutionary algorithm based on decomposition for high-dimensional multi-objective problems," IEEE Trans. Evol. Comput., vol. 26, no. 6, pp. 1581-1595, Dec. 2022.

[21] X. Wu, Q. Lin, J. Li, K. C. Tan, and V. C. Leung, "An ensemble surrogate-based coevolutionary algorithm for solving large-scale expensive optimization problems," IEEE Trans. Cybern., early access, Sep. 16, 2022, doi: 10.1109/TCYB.2022.3200517.

[22] M. W. Hoffman, E. Brochu, and N. De Freitas, "Portfolio allocation for Bayesian optimization," in Proc. UAI, 2011, pp. 327-336.

[23] Z. Song, H. Wang, C. He, and Y. Jin, "A Kriging-assisted two-archive evolutionary algorithm for expensive many-objective optimization," IEEE Trans. Evol. Comput., vol. 25, no. 6, pp. 1013-1027, Dec. 2021.

[24] Z. Liu, H. Wang, and Y. Jin, "Performance indicator-based adaptive model selection for offline data-driven multiobjective evolutionary optimization," IEEE Trans. Cybern., early access, May 13, 2022, doi: 10.1109/TCYB.2022.3170344.

[25] H. Zhen, W. Gong, and L. Wang, "Evolutionary sampling agent for expensive problems," IEEE Trans. Evol. Comput., vol. 27, no. 3, pp. 716-727, Jun. 2023.

[26] Q. Zhang, W. Liu, E. Tsang, and B. Virginas, "Expensive multiobjective optimization by MOEA/D with Gaussian process model," IEEE Trans. Evol. Comput., vol. 14, no. 3, pp. 456-474, Jun. 2010.

[27] J. E. Dennis and V. Torczon, "Managing approximation models in optimization," Multidiscip. Design Optim. State-Art, vol. 5, pp. 330-347, Nov. 1998.

[28] G. Li, Q. Zhang, Q. Lin, and W. Gao, "A three-level radial basis function method for expensive optimization," IEEE Trans. Cybern., vol. 52, no. 7, pp. 5720-5731, Jul. 2022.

[29] J. Zhang, A. Zhou, and G. Zhang, "A multiobjective evolutionary algorithm based on decomposition and preselection," in Bio-Inspired Computing-Theories and Applications. Heidelberg, Germany: Springer, 2015, pp. 631-642.

[30] M. Yu, X. Li, and J. Liang, "A dynamic surrogate-assisted evolutionary algorithm framework for expensive structural optimization," Struct. Multidiscip. Optim., vol. 61, no. 2, pp. 711-729, 2020.

[31] H. Zhen, W. Gong, L. Wang, F. Ming, and Z. Liao, "Two-stage data-driven evolutionary optimization for high-dimensional expensive problems," IEEE Trans. Cybern., vol. 53, no. 4, pp. 2368-2379, Apr. 2023.

[32] X. Wang, G. G. Wang, B. Song, P. Wang, and Y. Wang, "A novel evolutionary sampling assisted optimization method for high-dimensional expensive problems," IEEE Trans. Evol. Comput., vol. 23, no. 5, pp. 815-827, Oct. 2019.

[33] X. Cai, L. Gao, and X. Li, "Efficient generalized surrogate-assisted evolutionary algorithm for high-dimensional expensive problems," IEEE Trans. Evol. Comput., vol. 24, no. 2, pp. 365-379, Apr. 2020.

[34] Z. Wang et al., "Multiobjective optimization-aided decision-making system for large-scale manufacturing planning," IEEE Trans. Cybern., vol. 52, no. 8, pp. 8326-8339, Aug. 2022.

[35] D. Guo, Y. Jin, J. Ding, and T. Chai, "Heterogeneous ensemble-based infill criterion for evolutionary multiobjective optimization of expensive problems," IEEE Trans. Cybern., vol. 49, no. 3, pp. 1012-1025, Mar. 2019.

[36] J.-Y. Li, Z.-H. Zhan, H. Wang, and J. Zhang, "Data-driven evolutionary algorithm with perturbation-based ensemble surrogates," IEEE Trans. Cybern., vol. 51, no. 8, pp. 3925-3937, Aug. 2021.

[37] L. Kocsis and C. Szepesvári, "Bandit based Monte-Carlo planning," in Proc. Eur. Conf. Mach. Learn., 2006, pp. 282-293.

[38] S. Pandey, D. Chakrabarti, and D. Agarwal, "Multi-armed bandit problems with dependent arms," in Proc. 24th Int. Conf. Mach. Learn., 2007, pp. 721-728.

[39] E. Carlsson, D. Dubhashi, and F. D. Johansson, "Thompson sampling for bandits with clustered arms," 2021, arXiv:2109.01656.

[40] D. R. Jones, M. Schonlau, and W. J. Welch, "Efficient global optimization of expensive black-box functions," J. Global Optim., vol. 13, no. 4, p. 455, 1998.

[41] P. I. Frazier, "A tutorial on Bayesian optimization," 2018, arXiv:1807.02811.

[42] D. Zhan and H. Xing, "A fast Kriging-assisted evolutionary algorithm based on incremental learning," IEEE Trans. Evol. Comput., vol. 25, no. 5, pp. 941-955, Oct. 2021.

[43] D. D. Cox and S. John, "A statistical method for global optimization," in Proc. IEEE Int. Conf. Syst., Man, Cybern., 1992, pp. 1241-1246.

[44] J. Zhang, A. Zhou, and G. Zhang, "A classification and Pareto domination based multiobjective evolutionary algorithm," in Proc. IEEE Congr. Evol. Comput. (CEC), 2015, pp. 2883-2890.

[45] C. Sun, Y. Jin, R. Cheng, J. Ding, and J. Zeng, "Surrogate-assisted cooperative swarm optimization of high-dimensional expensive problems," IEEE Trans. Evol. Comput., vol. 21, no. 4, pp. 644-660, Aug. 2017.

[46] Y. Haibo, T. Ying, Z. Jianchao, S. Chaoli, and J. Yaochu, "Surrogate-assisted hierarchical particle swarm optimization," Inf. Sci., vols. 454-455, pp. 59-72, Jul. 2018.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->




<!-- Meanless: 1126 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024-->

[47] F.-F. Wei et al., "A classifier-assisted level-based learning swarm optimizer for expensive optimization," IEEE Trans. Evol. Comput., vol. 25, no. 2, pp. 219-233, Apr. 2020.

[48] Q. Liu, R. Cheng, Y. Jin, M. Heiderich, and T. Rodemann, "Reference vector-assisted adaptive model management for surrogate-assisted many-objective optimization," IEEE Trans. Syst., Man, Cybern., Syst., vol. 52, no. 12, pp. 7760-7773, Dec. 2022.

[49] G. Li, Q. Zhang, J. Sun, and Z. Han, "Radial basis function assisted optimization method with batch infill sampling criterion for expensive optimization," in Proc. IEEE Congr. Evol. Comput. (CEC), 2019, pp. 1664-1671.

[50] A. I. Khuri and S. Mukhopadhyay, "Response surface methodology," Wiley Interdiscipl. Rev. Comput. Stat., vol. 2, no. 2, pp. 128-149, 2010.

[51] W. Gao, G. Li, Q. Zhang, Y. Luo, and Z. Wang, "Solving nonlinear equation systems by a two-phase evolutionary algorithm," IEEE Trans. Syst., Man, Cybern., Syst., vol. 51, no. 9, pp. 5652-5663, Sep. 2021.

[52] L. Pan, C. He, Y. Tian, H. Wang, X. Zhang, and Y. Jin, "A classification-based surrogate-assisted evolutionary algorithm for expensive many-objective optimization," IEEE Trans. Evol. Comput., vol. 23, no. 1, pp. 74-88, Feb. 2019.

[53] T. Zhao, M. Li, and M. Poloczek, "Fast reconfigurable antenna state selection with hierarchical Thompson sampling," in Proc. IEEE Int. Conf. Commun. (ICC), 2019, pp. 1-6.

[54] R. Singh, F. Liu, Y. Sun, and N. Shroff, "Multi-armed bandits with dependent arms," 2020, arXiv:2010.09478.

[55] J. Hong, B. Kveton, M. Zaheer, and M. Ghavamzadeh, "Hierarchical Bayesian bandits," in Proc. Int. Conf. Artif. Intell. Stat., 2022, pp. 7724-7741.

[56] P. N. Suganthan et al., "Problem definitions and evaluation criteria for the CEC 2005 special session on real-parameter optimization," Nanyang Technol. Univ., Singapore, IIT, Kanpur, India, KanGAL Rep. #2005005, 2005.

[57] J. Liang, B. Qu, P. Suganthan, and Q. Chen, "Problem definitions and evaluation criteria for the CEC 2015 competition on learning-based real-parameter single objective optimization," Comput. Intell. Lab., Nanyang Technol. Univ., Singapore, Rep. 201411A, 2014.

[58] G. Li, Z. Wang, and M. Gong, "Expensive optimization via surrogate-assisted and model-free evolutionary optimization," IEEE Trans. Syst., Man, Cybern., Syst., vol. 53, no. 5, pp. 2758-2769, May 2023.

[59] Z. Wang, Y.-S. Ong, and H. Ishibuchi, "On scalable multiobjective test problems with hardly dominated boundaries," IEEE Trans. Evol. Comput., vol. 23, no. 2, pp. 217-231, Apr. 2019.

[60] G. Chen, X. Luo, J. J. Jiao, and X. Xue, "Data-driven evolutionary algorithm for oil reservoir well-placement and control optimization," Fuel, vol. 326, Oct. 2022, Art. no. 125125.

[61] J.-D. Jansen, R.-M. Fonseca, S. Kahrobaei, M. Siraj, G. Van Essen, and P. Van den Hof, "The egg model-A geological ensemble for reservoir simulation," Geosci. Data J., vol. 1, no. 2, pp. 192-195, 2014.

[62] K.-A. Lie, An Introduction to Reservoir Simulation Using MATLAB/GNU Octave: User Guide for the MATLAB Reservoir Simulation Toolbox (MRST). Cambridge, U.K.: Cambridge Univ. Press, 2019.

<!-- Media --><img src="data:image/jpeg;base64,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"/>

<!-- Media -->

Lindong Xie received the B.S. degree in mechanical and electronic engineering from Henan University of Science and Technology, Luoyang, China, in 2021. He is currently pursuing the M.S. degree with the School of System Design and Intelligent Manufacturing, Southern University of Science and Technology, Shenzhen, China.

His current research interests include data-driven evolutionary algorithms, and machine learning and their applications.

<!-- Media --><img src="data:image/jpeg;base64,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"/>

<!-- Media -->

Genghui Li received the M.Sc. degree in computer science and technology from Shenzhen University, Shenzhen, China, in 2016, and the Ph.D. degree in computer science from the City University of Hong Kong, Hong Kong, China, in 2021.

He is currently a Postdoctoral Fellow with the Southern University of Science and Technology, Shenzhen. His research interests include evolutionary computation, computational intelligence, and machine learning.

<!-- Media --><img src="data:image/jpeg;base64,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"/>

<!-- Media -->

Zhenkun Wang (Member, IEEE) received the Ph.D. degree in circuits and systems from Xidian University, Xi'an, China, in 2016.

From 2017 to 2020, he was a Postdoctoral Research Fellow with the School of Computer Science and Engineering, Nanyang Technological University, Singapore, and with the Department of Computer Science, City University of Hong Kong, Hong Kong. He is currently an Assistant Professor with the School of System Design and Intelligent Manufacturing and the Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China. His research interests include evolutionary computation, optimization, machine learning, and their applications.

Dr. Wang is an Associate Editor of the Swarm and Evolutionary Computation.

<!-- Media --><img src="data:image/jpeg;base64,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"/>

<!-- Media -->

Laizhong Cui (Senior Member, IEEE) received the B.S. degree from Jilin University, Changchun, China, in 2007, and the Ph.D. degree in computer science and technology from Tsinghua University, Beijing, China, in 2012.

He is currently a Professor with the College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, China. He led more than ten scientific research projects, including National Key Research and Development Plan of China, National Natural Science Foundation of China, Guangdong Natural Science Foundation of China, and Shenzhen Basic Research Plan. He has published more than 100 papers, including IEEE JOURNAL ON SELECTED AREAS IN COMMUNICATIONS, IEBEE TRANSACTIONS ON COMPUTERS, IEEE TRANSACTIONS ON PARALLEL AND DISTRIBUTED SYSTEMS, IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, IEEE TRANSACTIONS ON MULTIMEDIA, IEEE INTERNET OF THINGS JOURNAL, IEEE TRANSACTIONS ON INDUSTRIAL INFORMATICS, IEEE TRANSACTIONS ON VEHICULAR TECHNOLOGY, IEEE TRANSACTIONS ON NETWORK AND SERVICE MANAGEMENT, ACM Transactions on Internet Technology, IEEE NETWORK, IEEE INFOCOM, ACM MM, IEEE ICNP, and IEEE ICDCS. His research interests include future Internet architecture and protocols, edge computing, multimedia systems and applications, Blockchain, Internet of Things, cloud computing, and federated learning.

Prof. Cui serves as an Associate Editor or a member of Editorial Board for several international journals, including IEEE INTERNET OF THINGS JOURNAL, IEEE TRANSACTIONS ON CLOUD COMPUTING, IEEE TRANSACTIONS ON NETWORK AND SERVICE MANAGEMENT, and International Journal of Machine Learning and Cybernetics. He is a Distinguished Member of the CCF.

<!-- Media --><img src="data:image/jpeg;base64,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"/>

<!-- Media -->

Maoguo Gong (Senior Member, IEEE) received the B.Eng. degree (Hons.) in electronic engineering and Ph.D. degree in electronic science and technology from Xidian University, Xi'an, China, in 2003 and 2009, respectively.

Since 2006, he has been a Teacher with Xidian University. He was promoted to an Associate Professor and a Full Professor in 2008 and 2010, respectively, both with exceptive admission. He is leading or has completed over 20 projects as the Principle Investigator, funded by the National Natural Science Foundation of China and the National Key Research and Development Program of China. He has published over 100 papers in journals and conferences, and holds over 20 granted patents as the first inventor. His research interests are broadly in the area of computational intelligence, with applications to optimization, learning, data mining, and image understanding.

Dr. Gong is the Director of the Chinese Association for Artificial Intelligence-Youth Branch, the Senior Member of Chinese Computer Federation, and an Associate Editor or an Editorial Board Member for over five journals, including the IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS and the IEEE TRANSACTIONS ON EMERGING TOPICS IN COMPUTATIONAL INTELLIGENCE.

<!-- Meanless: Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.-->

