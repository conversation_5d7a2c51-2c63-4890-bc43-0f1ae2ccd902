"""
联邦多任务优化测试脚本
简化版本用于验证核心功能
"""

import numpy as np
from federated_multitask_coordinator import FederatedMultiTaskCoordinator

def simple_quadratic_1(x):
    """简单二次函数1: (x-1)^2"""
    return np.sum((x - 1)**2)

def simple_quadratic_2(x):
    """简单二次函数2: (x-1.5)^2"""
    return np.sum((x - 1.5)**2)

def simple_quadratic_3(x):
    """简单二次函数3: (x-0.5)^2"""
    return np.sum((x - 0.5)**2)

def rosenbrock_shifted(x):
    """平移的Rosenbrock函数"""
    x_shifted = x - 0.5
    return np.sum(100 * (x_shifted[1:] - x_shifted[:-1]**2)**2 + (1 - x_shifted[:-1])**2)

def test_basic_functionality():
    """测试基本功能"""
    print("测试联邦多任务优化基本功能")
    print("="*40)
    
    # 设置问题维度和边界
    dimension = 3
    bounds = np.array([[-3, 3]] * dimension)
    
    # 创建协调器
    coordinator = FederatedMultiTaskCoordinator(
        global_bounds=bounds,
        max_parallel_tasks=2
    )
    
    # 注册任务
    tasks = {
        'quad_1': (simple_quadratic_1, 'quadratic'),
        'quad_2': (simple_quadratic_2, 'quadratic'),
        'quad_3': (simple_quadratic_3, 'quadratic'),
        'rosenbrock': (rosenbrock_shifted, 'rosenbrock')
    }
    
    print(f"注册 {len(tasks)} 个任务...")
    for task_id, (objective, task_type) in tasks.items():
        success = coordinator.register_task(
            task_id=task_id,
            objective_func=objective,
            task_type=task_type,
            population_size=15,
            max_fes=100
        )
        print(f"  {task_id} ({'成功' if success else '失败'})")
    
    # 执行优化
    print(f"\n开始优化...")
    results = coordinator.optimize_all_tasks(parallel=False)  # 使用顺序执行便于调试
    
    # 显示结果
    print(f"\n优化结果:")
    for task_id, result in results.items():
        if 'error' not in result:
            print(f"  {task_id}:")
            print(f"    最佳解: {result['best_solution']}")
            print(f"    最佳适应度: {result['best_fitness']:.6f}")
            print(f"    FEs: {result['statistics']['total_fes']}")
        else:
            print(f"  {task_id}: 失败 - {result['error']}")
    
    # 分析任务关系
    print(f"\n任务关系分析:")
    relationships = coordinator.analyze_task_relationships()
    if 'high_similarity_pairs' in relationships:
        for pair in relationships['high_similarity_pairs']:
            print(f"  {pair['task1']} ↔ {pair['task2']}: {pair['similarity']:.3f}")
    
    return coordinator, results

def test_knowledge_sharing():
    """测试知识共享机制"""
    print("\n" + "="*40)
    print("测试知识共享机制")
    print("="*40)
    
    dimension = 2
    bounds = np.array([[-2, 2]] * dimension)
    
    coordinator = FederatedMultiTaskCoordinator(
        global_bounds=bounds,
        max_parallel_tasks=1
    )
    
    # 注册两个相似任务
    coordinator.register_task('task_a', simple_quadratic_1, 'quadratic', 10, 50)
    coordinator.register_task('task_b', simple_quadratic_2, 'quadratic', 10, 50)
    
    # 先优化第一个任务
    print("优化第一个任务...")
    task_a = coordinator.tasks['task_a']
    
    # 手动初始化和运行几步
    initial_pop = task_a._latin_hypercube_sampling(10)
    for x in initial_pop:
        task_a._evaluate_solution(x)
    
    # 运行几次迭代
    for i in range(10):
        current_pop, current_fitness = task_a._get_current_population()
        current_population = np.column_stack([current_pop, current_fitness])
        f_min = np.min(current_fitness)
        
        model_name, criterion_name = task_a.mab.select_arms(i+1)
        try:
            new_x = task_a._generate_new_solution(model_name, criterion_name,
                                                current_population, f_min)
            new_fitness = task_a._evaluate_solution(new_x)
            task_a.mab.update_rewards(model_name, criterion_name, current_population,
                                    new_x, new_fitness)
        except:
            new_x = np.random.uniform(bounds[:, 0], bounds[:, 1])
            task_a._evaluate_solution(new_x)
    
    print(f"任务A完成，最佳适应度: {min(sol[-1] for sol in task_a.database):.6f}")
    
    # 更新服务器知识
    report_a = task_a.prepare_knowledge_report()
    coordinator.server.update_task_knowledge(
        'task_a', report_a['best_solutions'], report_a['fitness_values'],
        report_a['model_performance'], report_a['criterion_performance']
    )
    
    # 为第二个任务获取知识
    knowledge_b = coordinator.server.get_task_initialization_knowledge('task_b')
    print(f"\n任务B获得的知识:")
    print(f"  相似任务: {knowledge_b.get('similar_tasks', [])}")
    print(f"  初始化解数量: {len(knowledge_b.get('initialization_solutions', []))}")
    
    # 初始化第二个任务
    task_b = coordinator.tasks['task_b']
    task_b.initialize_from_knowledge(knowledge_b)
    
    # 运行第二个任务
    print(f"\n优化第二个任务（使用知识）...")

    # 先确保有初始种群
    if len(task_b.database) == 0:
        initial_pop = task_b._latin_hypercube_sampling(5)
        for x in initial_pop:
            task_b._evaluate_solution(x)

    for i in range(15):
        current_pop, current_fitness = task_b._get_current_population()
        if len(current_pop) == 0:
            continue
            
        current_population = np.column_stack([current_pop, current_fitness])
        f_min = np.min(current_fitness)
        
        model_name, criterion_name = task_b.mab.select_arms(i+1)
        try:
            new_x = task_b._generate_new_solution(model_name, criterion_name,
                                                current_population, f_min)
            new_fitness = task_b._evaluate_solution(new_x)
            task_b.mab.update_rewards(model_name, criterion_name, current_population,
                                    new_x, new_fitness)
        except:
            new_x = np.random.uniform(bounds[:, 0], bounds[:, 1])
            task_b._evaluate_solution(new_x)
    
    print(f"任务B完成，最佳适应度: {min(sol[-1] for sol in task_b.database):.6f}")
    
    # 比较两个任务的性能
    best_a = min(sol[-1] for sol in task_a.database)
    best_b = min(sol[-1] for sol in task_b.database)
    
    print(f"\n性能比较:")
    print(f"  任务A (无知识): {best_a:.6f}")
    print(f"  任务B (有知识): {best_b:.6f}")
    print(f"  知识迁移效果: {'有效' if best_b < best_a * 1.1 else '有限'}")

def main():
    """主测试函数"""
    print("联邦多任务优化测试")
    print("="*50)
    
    try:
        # 测试基本功能
        coordinator, results = test_basic_functionality()
        
        # 测试知识共享
        test_knowledge_sharing()
        
        print(f"\n" + "="*50)
        print("所有测试完成!")
        
        # 显示最终总结
        if coordinator:
            coordinator.print_summary()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
