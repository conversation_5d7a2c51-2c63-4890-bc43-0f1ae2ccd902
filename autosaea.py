import numpy as np
import warnings
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Callable
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, ConstantKernel as C
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt

warnings.filterwarnings('ignore')

class SurrogateModel(ABC):
    """Abstract base class for surrogate models"""
    
    @abstractmethod
    def fit(self, X: np.ndarray, y: np.ndarray):
        """Train the surrogate model"""
        pass
    
    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict using the surrogate model"""
        pass

class GPModel(SurrogateModel):
    """Gaussian Process Model"""
    
    def __init__(self):
        kernel = C(1.0, (1e-3, 1e3)) * RBF(1.0, (1e-2, 1e2))
        self.model = GaussianProcessRegressor(kernel=kernel, alpha=1e-6, normalize_y=True)
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        X_scaled = self.scaler_X.fit_transform(X)
        y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).ravel()
        self.model.fit(X_scaled, y_scaled)
        
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        X_scaled = self.scaler_X.transform(X)
        y_pred_scaled, std_scaled = self.model.predict(X_scaled, return_std=True)
        y_pred = self.scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).ravel()
        # Scale std back approximately
        std = std_scaled * self.scaler_y.scale_[0]
        return y_pred, std

class RBFModel(SurrogateModel):
    """Radial Basis Function Model"""
    
    def __init__(self):
        self.weights = None
        self.X_train = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        
    def _rbf_kernel(self, X1: np.ndarray, X2: np.ndarray) -> np.ndarray:
        """Cubic RBF kernel"""
        distances = cdist(X1, X2, 'euclidean')
        return distances ** 3
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        self.X_train = self.scaler_X.fit_transform(X)
        y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).ravel()
        
        n, d = X.shape
        # Build the system matrix
        Phi = self._rbf_kernel(self.X_train, self.X_train)
        P = np.column_stack([np.ones(n), self.X_train])
        
        # Construct the full system
        A = np.block([[Phi, P], [P.T, np.zeros((d+1, d+1))]])
        b = np.concatenate([y_scaled, np.zeros(d+1)])
        
        # Solve the system
        try:
            solution = np.linalg.solve(A, b)
            self.weights = solution[:n]
            self.poly_coeffs = solution[n:]
        except np.linalg.LinAlgError:
            # Use pseudo-inverse if singular
            solution = np.linalg.pinv(A) @ b
            self.weights = solution[:n]
            self.poly_coeffs = solution[n:]
            
    def predict(self, X: np.ndarray) -> np.ndarray:
        X_scaled = self.scaler_X.transform(X)
        n_test = X.shape[0]
        
        # RBF part
        Phi_test = self._rbf_kernel(X_scaled, self.X_train)
        rbf_part = Phi_test @ self.weights
        
        # Polynomial part
        P_test = np.column_stack([np.ones(n_test), X_scaled])
        poly_part = P_test @ self.poly_coeffs
        
        y_pred_scaled = rbf_part + poly_part
        y_pred = self.scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).ravel()
        return y_pred

class PRSModel(SurrogateModel):
    """Polynomial Response Surface Model"""
    
    def __init__(self):
        self.coeffs = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        
    def _build_polynomial_matrix(self, X: np.ndarray) -> np.ndarray:
        """Build second-order polynomial matrix"""
        n, d = X.shape
        # Number of terms: 1 + d + d*(d+1)/2
        n_terms = 1 + d + d * (d + 1) // 2
        P = np.zeros((n, n_terms))
        
        idx = 0
        # Constant term
        P[:, idx] = 1
        idx += 1
        
        # Linear terms
        P[:, idx:idx+d] = X
        idx += d
        
        # Quadratic terms
        for i in range(d):
            for j in range(i, d):
                P[:, idx] = X[:, i] * X[:, j]
                idx += 1
                
        return P
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        X_scaled = self.scaler_X.fit_transform(X)
        y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).ravel()
        
        P = self._build_polynomial_matrix(X_scaled)
        
        # Solve using least squares
        try:
            self.coeffs = np.linalg.lstsq(P, y_scaled, rcond=None)[0]
        except np.linalg.LinAlgError:
            self.coeffs = np.linalg.pinv(P) @ y_scaled
            
    def predict(self, X: np.ndarray) -> np.ndarray:
        X_scaled = self.scaler_X.transform(X)
        P = self._build_polynomial_matrix(X_scaled)
        y_pred_scaled = P @ self.coeffs
        y_pred = self.scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).ravel()
        return y_pred

class KNNModel(SurrogateModel):
    """K-Nearest Neighbors Model for classification"""
    
    def __init__(self, k=1, n_levels=5):
        self.k = k
        self.n_levels = n_levels
        self.model = KNeighborsClassifier(n_neighbors=k)
        self.scaler_X = StandardScaler()
        self.y_train = None
        
    def fit(self, X: np.ndarray, y: np.ndarray):
        X_scaled = self.scaler_X.fit_transform(X)
        self.y_train = y.copy()
        
        # Divide population into levels based on fitness
        n = len(y)
        sorted_indices = np.argsort(y)
        levels = np.zeros(n, dtype=int)
        
        level_size = n // self.n_levels
        for i in range(self.n_levels):
            start_idx = i * level_size
            end_idx = (i + 1) * level_size if i < self.n_levels - 1 else n
            levels[sorted_indices[start_idx:end_idx]] = i + 1
            
        self.model.fit(X_scaled, levels)
        
    def predict(self, X: np.ndarray) -> np.ndarray:
        X_scaled = self.scaler_X.transform(X)
        return self.model.predict(X_scaled)

class InfillCriterion(ABC):
    """Abstract base class for infill criteria"""
    
    @abstractmethod
    def select_solution(self, model: SurrogateModel, candidates: np.ndarray, 
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        """Select the best candidate solution"""
        pass

class LCBCriterion(InfillCriterion):
    """Lower Confidence Bound criterion"""
    
    def __init__(self, w=2.0):
        self.w = w
        
    def select_solution(self, model: GPModel, candidates: np.ndarray, 
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        y_pred, std = model.predict(candidates)
        lcb_values = y_pred - self.w * std
        return np.argmin(lcb_values)

class EICriterion(InfillCriterion):
    """Expected Improvement criterion"""
    
    def select_solution(self, model: GPModel, candidates: np.ndarray, 
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        y_pred, std = model.predict(candidates)
        
        # Avoid division by zero
        std = np.maximum(std, 1e-9)
        
        z = (f_min - y_pred) / std
        ei_values = (f_min - y_pred) * self._normal_cdf(z) + std * self._normal_pdf(z)
        return np.argmax(ei_values)
    
    def _normal_pdf(self, x):
        return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)
    
    def _normal_cdf(self, x):
        return 0.5 * (1 + np.sign(x) * np.sqrt(1 - np.exp(-2 * x**2 / np.pi)))

class PrescreeningCriterion(InfillCriterion):
    """Prescreening criterion for RBF/PRS models"""
    
    def select_solution(self, model: SurrogateModel, candidates: np.ndarray, 
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        y_pred = model.predict(candidates)
        return np.argmin(y_pred)

class LocalSearchCriterion(InfillCriterion):
    """Local search criterion for RBF/PRS models"""
    
    def __init__(self, max_iter=1000):
        self.max_iter = max_iter
        
    def select_solution(self, model: SurrogateModel, candidates: np.ndarray, 
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        # Define local search bounds based on current population
        lb = np.min(population, axis=0)
        ub = np.max(population, axis=0)
        
        # Ensure bounds are within global bounds
        lb = np.maximum(lb, bounds[:, 0])
        ub = np.minimum(ub, bounds[:, 1])
        
        def objective(x):
            return model.predict(x.reshape(1, -1))[0]
        
        # Start from the best candidate
        best_candidate_idx = np.argmin(model.predict(candidates))
        x0 = candidates[best_candidate_idx]
        
        # Perform local optimization
        result = minimize(objective, x0, bounds=list(zip(lb, ub)), 
                         method='L-BFGS-B', options={'maxiter': self.max_iter})
        
        # Return the index of the closest candidate to the optimum
        if result.success:
            distances = np.linalg.norm(candidates - result.x, axis=1)
            return np.argmin(distances)
        else:
            return best_candidate_idx

class L1ExploitationCriterion(InfillCriterion):
    """L1-Exploitation criterion for KNN model"""

    def select_solution(self, model: KNNModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        # Predict levels for candidates
        levels = model.predict(candidates)

        # Find L1 (best level) candidates
        l1_indices = np.where(levels == 1)[0]
        if len(l1_indices) == 0:
            # If no L1 candidates, return the one with best predicted level
            return np.argmin(levels)

        l1_candidates = candidates[l1_indices]

        # Find L1 solutions in current population
        y_train = model.y_train
        n = len(y_train)
        sorted_indices = np.argsort(y_train)
        level_size = n // model.n_levels
        l1_pop_indices = sorted_indices[:level_size]
        l1_population = population[l1_pop_indices]

        # Find the L1 candidate that maximizes minimum distance to L1 population
        max_min_dist = -1
        best_idx = 0

        for i, candidate in enumerate(l1_candidates):
            distances = np.linalg.norm(l1_population - candidate, axis=1)
            min_dist = np.min(distances)
            if min_dist > max_min_dist:
                max_min_dist = min_dist
                best_idx = i

        return l1_indices[best_idx]

class L1ExplorationCriterion(InfillCriterion):
    """L1-Exploration criterion for KNN model"""

    def select_solution(self, model: KNNModel, candidates: np.ndarray,
                       population: np.ndarray, f_min: float, bounds: np.ndarray) -> int:
        # Predict levels for candidates
        levels = model.predict(candidates)

        # Find L1 (best level) candidates
        l1_indices = np.where(levels == 1)[0]
        if len(l1_indices) == 0:
            # If no L1 candidates, return the one with best predicted level
            return np.argmin(levels)

        l1_candidates = candidates[l1_indices]

        # Find L1 solutions in current population
        y_train = model.y_train
        n = len(y_train)
        sorted_indices = np.argsort(y_train)
        level_size = n // model.n_levels
        l1_pop_indices = sorted_indices[:level_size]
        l1_population = population[l1_pop_indices]

        # Find the L1 candidate that minimizes maximum distance to L1 population
        min_max_dist = float('inf')
        best_idx = 0

        for i, candidate in enumerate(l1_candidates):
            distances = np.linalg.norm(l1_population - candidate, axis=1)
            max_dist = np.max(distances)
            if max_dist < min_max_dist:
                min_max_dist = max_dist
                best_idx = i

        return l1_indices[best_idx]

class DEOperator:
    """Differential Evolution Operator"""

    def __init__(self, F=0.5, CR=0.9):
        self.F = F
        self.CR = CR

    def generate_offspring(self, population: np.ndarray, bounds: np.ndarray) -> np.ndarray:
        """Generate offspring using DE mutation and crossover"""
        n, d = population.shape
        offspring = np.zeros_like(population)

        for i in range(n):
            # Select three random individuals (different from current)
            candidates = list(range(n))
            candidates.remove(i)
            r1, r2, r3 = np.random.choice(candidates, 3, replace=False)

            # Find best individual
            best_idx = 0  # Assuming population is sorted, best is first

            # DE/best/1 mutation
            mutant = population[i] + self.F * (population[best_idx] - population[i]) + \
                    self.F * (population[r1] - population[r2])

            # Crossover
            j_rand = np.random.randint(d)
            for j in range(d):
                if np.random.random() <= self.CR or j == j_rand:
                    offspring[i, j] = mutant[j]
                else:
                    offspring[i, j] = population[i, j]

            # Boundary handling
            offspring[i] = np.clip(offspring[i], bounds[:, 0], bounds[:, 1])

        return offspring

class TwoLevelMAB:
    """Two-Level Multi-Armed Bandit for model and criterion selection"""

    def __init__(self, alpha=2.0):
        self.alpha = alpha

        # High-level arms (models)
        self.high_arms = ['GP', 'RBF', 'PRS', 'KNN']

        # Low-level arms (criteria) and their associations
        self.low_arms = ['LCB', 'EI', 'prescreening_rbf', 'localsearch_rbf',
                        'prescreening_prs', 'localsearch_prs', 'L1_exploitation', 'L1_exploration']

        # Association between high and low level arms
        self.associations = {
            'GP': ['LCB', 'EI'],
            'RBF': ['prescreening_rbf', 'localsearch_rbf'],
            'PRS': ['prescreening_prs', 'localsearch_prs'],
            'KNN': ['L1_exploitation', 'L1_exploration']
        }

        # Initialize values and selection counts
        self.high_values = {arm: 0.0 for arm in self.high_arms}
        self.low_values = {arm: 0.0 for arm in self.low_arms}
        self.high_counts = {arm: 0 for arm in self.high_arms}
        self.low_counts = {arm: 0 for arm in self.low_arms}

        # Combinatorial arms for initial exploration
        self.combinatorial_arms = [
            ('GP', 'LCB'), ('GP', 'EI'),
            ('RBF', 'prescreening_rbf'), ('RBF', 'localsearch_rbf'),
            ('PRS', 'prescreening_prs'), ('PRS', 'localsearch_prs'),
            ('KNN', 'L1_exploitation'), ('KNN', 'L1_exploration')
        ]

    def select_arms(self, t: int) -> Tuple[str, str]:
        """Select high-level and low-level arms using TL-UCB"""

        # Initial exploration phase
        if t <= len(self.combinatorial_arms):
            return self.combinatorial_arms[t-1]

        # TL-UCB selection
        # Select high-level arm
        high_ucb_values = {}
        for arm in self.high_arms:
            if self.high_counts[arm] == 0:
                high_ucb_values[arm] = float('inf')
            else:
                confidence = np.sqrt(self.alpha * np.log(t) / self.high_counts[arm])
                high_ucb_values[arm] = self.high_values[arm] + confidence

        selected_high = max(high_ucb_values, key=high_ucb_values.get)

        # Select low-level arm from associated arms
        associated_low_arms = self.associations[selected_high]
        low_ucb_values = {}

        for arm in associated_low_arms:
            if self.low_counts[arm] == 0:
                low_ucb_values[arm] = float('inf')
            else:
                confidence = np.sqrt(self.alpha * np.log(t) / self.low_counts[arm])
                low_ucb_values[arm] = self.low_values[arm] + confidence

        selected_low = max(low_ucb_values, key=low_ucb_values.get)

        return selected_high, selected_low

    def update_rewards(self, high_arm: str, low_arm: str, population: np.ndarray,
                      new_solution: np.ndarray, new_fitness: float):
        """Update rewards using TL-R mechanism"""

        # Calculate ranking of new solution
        population_fitness = np.array([sol[-1] for sol in population])  # Assuming fitness is last element
        ranking = np.sum(population_fitness < new_fitness) + 1
        n = len(population)

        # Low-level reward
        low_reward = -1/n * ranking + (n+1)/n

        # Update low-level arm value
        old_low_value = self.low_values[low_arm]
        self.low_counts[low_arm] += 1
        self.low_values[low_arm] = (self.low_counts[low_arm] - 1) * old_low_value + low_reward
        self.low_values[low_arm] /= self.low_counts[low_arm]

        # High-level reward
        value_change = self.low_values[low_arm] - old_low_value
        n_associated = len(self.associations[high_arm])
        high_reward = value_change / n_associated

        # Update high-level arm value
        self.high_counts[high_arm] += 1
        self.high_values[high_arm] += high_reward

class AutoSAEA:
    """AutoSAEA: Surrogate-Assisted Evolutionary Algorithm with Auto-Configuration"""

    def __init__(self, objective_func: Callable, bounds: np.ndarray,
                 population_size: int = 50, max_fes: int = 500, alpha: float = 2.0):
        """
        Initialize AutoSAEA

        Args:
            objective_func: The expensive objective function to minimize
            bounds: Array of shape (d, 2) with lower and upper bounds for each dimension
            population_size: Size of the population
            max_fes: Maximum number of function evaluations
            alpha: UCB parameter for exploration-exploitation balance
        """
        self.objective_func = objective_func
        self.bounds = bounds
        self.population_size = population_size
        self.max_fes = max_fes
        self.alpha = alpha

        self.dimension = bounds.shape[0]
        self.database = []  # Store all evaluated solutions
        self.fes_count = 0

        # Initialize components
        self.mab = TwoLevelMAB(alpha=alpha)
        self.de_operator = DEOperator()

        # Initialize models and criteria
        self.models = {
            'GP': GPModel(),
            'RBF': RBFModel(),
            'PRS': PRSModel(),
            'KNN': KNNModel()
        }

        self.criteria = {
            'LCB': LCBCriterion(),
            'EI': EICriterion(),
            'prescreening_rbf': PrescreeningCriterion(),
            'localsearch_rbf': LocalSearchCriterion(),
            'prescreening_prs': PrescreeningCriterion(),
            'localsearch_prs': LocalSearchCriterion(),
            'L1_exploitation': L1ExploitationCriterion(),
            'L1_exploration': L1ExplorationCriterion()
        }

        # History tracking
        self.history = {
            'best_fitness': [],
            'selected_models': [],
            'selected_criteria': [],
            'model_values': {model: [] for model in self.models.keys()},
            'criterion_values': {criterion: [] for criterion in self.criteria.keys()}
        }

    def _latin_hypercube_sampling(self, n_samples: int) -> np.ndarray:
        """Generate initial population using Latin Hypercube Sampling"""
        samples = np.random.random((n_samples, self.dimension))

        for i in range(self.dimension):
            # Generate n_samples equally spaced intervals
            intervals = np.linspace(0, 1, n_samples + 1)
            # Randomly permute the intervals
            perm = np.random.permutation(n_samples)
            # Sample within each interval
            for j in range(n_samples):
                low = intervals[perm[j]]
                high = intervals[perm[j] + 1]
                samples[j, i] = low + (high - low) * samples[j, i]

        # Scale to actual bounds
        for i in range(self.dimension):
            samples[:, i] = self.bounds[i, 0] + samples[:, i] * (self.bounds[i, 1] - self.bounds[i, 0])

        return samples

    def _evaluate_solution(self, x: np.ndarray) -> float:
        """Evaluate a solution and update database"""
        fitness = self.objective_func(x)
        self.database.append(np.concatenate([x, [fitness]]))
        self.fes_count += 1
        return fitness

    def _get_current_population(self) -> Tuple[np.ndarray, np.ndarray]:
        """Get current best N solutions from database"""
        if len(self.database) <= self.population_size:
            data = np.array(self.database)
        else:
            # Sort by fitness and take best N
            data = np.array(self.database)
            sorted_indices = np.argsort(data[:, -1])
            data = data[sorted_indices[:self.population_size]]

        X = data[:, :-1]
        y = data[:, -1]
        return X, y

    def _generate_new_solution(self, model_name: str, criterion_name: str,
                              population: np.ndarray, f_min: float) -> np.ndarray:
        """Generate new solution using selected model and criterion"""

        # Train the selected model
        X_pop, y_pop = population[:, :-1], population[:, -1]
        model = self.models[model_name]
        model.fit(X_pop, y_pop)

        # Generate candidate solutions using DE
        candidates = self.de_operator.generate_offspring(X_pop, self.bounds)

        # Select best candidate using the criterion
        criterion = self.criteria[criterion_name]
        best_idx = criterion.select_solution(model, candidates, X_pop, f_min, self.bounds)

        return candidates[best_idx]

    def optimize(self) -> Tuple[np.ndarray, float]:
        """Run the AutoSAEA optimization"""

        print("Initializing AutoSAEA...")

        # Step 1: Initialize population using LHS
        initial_population = self._latin_hypercube_sampling(self.population_size)

        # Evaluate initial population
        for x in initial_population:
            self._evaluate_solution(x)

        print(f"Initial population evaluated. FEs: {self.fes_count}")

        # Main optimization loop
        t = 1
        while self.fes_count < self.max_fes:
            # Get current population
            current_pop, current_fitness = self._get_current_population()
            current_population = np.column_stack([current_pop, current_fitness])
            f_min = np.min(current_fitness)

            # Select model and criterion using TL-UCB
            model_name, criterion_name = self.mab.select_arms(t)

            # Generate new solution
            try:
                new_x = self._generate_new_solution(model_name, criterion_name,
                                                  current_population, f_min)

                # Evaluate new solution
                new_fitness = self._evaluate_solution(new_x)

                # Update MAB rewards
                self.mab.update_rewards(model_name, criterion_name, current_population,
                                      new_x, new_fitness)

                # Update history
                self.history['best_fitness'].append(np.min([sol[-1] for sol in self.database]))
                self.history['selected_models'].append(model_name)
                self.history['selected_criteria'].append(criterion_name)

                # Record current values
                for model in self.models.keys():
                    self.history['model_values'][model].append(self.mab.high_values[model])
                for criterion in self.criteria.keys():
                    self.history['criterion_values'][criterion].append(self.mab.low_values[criterion])

                if t % 50 == 0:
                    print(f"Iteration {t}, FEs: {self.fes_count}, Best fitness: {self.history['best_fitness'][-1]:.6f}")
                    print(f"Selected: {model_name} + {criterion_name}")

            except Exception as e:
                print(f"Error in iteration {t}: {e}")
                # Generate a random solution as fallback
                new_x = np.random.uniform(self.bounds[:, 0], self.bounds[:, 1])
                self._evaluate_solution(new_x)

            t += 1

        # Return best solution
        best_data = min(self.database, key=lambda x: x[-1])
        best_x = best_data[:-1]
        best_fitness = best_data[-1]

        print(f"\nOptimization completed!")
        print(f"Best fitness: {best_fitness:.6f}")
        print(f"Total FEs: {self.fes_count}")

        return best_x, best_fitness

    def plot_convergence(self):
        """Plot convergence curve"""
        plt.figure(figsize=(12, 4))

        plt.subplot(1, 2, 1)
        plt.plot(self.history['best_fitness'])
        plt.xlabel('Iteration')
        plt.ylabel('Best Fitness')
        plt.title('Convergence Curve')
        plt.grid(True)

        plt.subplot(1, 2, 2)
        # Plot model selection frequency
        models = self.history['selected_models']
        unique_models, counts = np.unique(models, return_counts=True)
        plt.bar(unique_models, counts)
        plt.xlabel('Model')
        plt.ylabel('Selection Count')
        plt.title('Model Selection Frequency')
        plt.xticks(rotation=45)

        plt.tight_layout()
        plt.show()

    def get_statistics(self) -> Dict:
        """Get optimization statistics"""
        stats = {
            'best_fitness': min(sol[-1] for sol in self.database),
            'total_fes': self.fes_count,
            'model_selection_counts': {},
            'criterion_selection_counts': {},
            'final_model_values': self.mab.high_values.copy(),
            'final_criterion_values': self.mab.low_values.copy()
        }

        # Count selections
        for model in self.models.keys():
            stats['model_selection_counts'][model] = self.history['selected_models'].count(model)

        for criterion in self.criteria.keys():
            stats['criterion_selection_counts'][criterion] = self.history['selected_criteria'].count(criterion)

        return stats
