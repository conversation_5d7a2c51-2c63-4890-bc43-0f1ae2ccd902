1114 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024
Surrogate-Assisted Evolutionary Algorithm With
Model and Infill Criterion Auto-Configuration
<PERSON><PERSON> , <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> , Member, IEEE, <PERSON><PERSON><PERSON> , Senior Member, IEEE, and <PERSON><PERSON><PERSON> , Senior Member, IEEE
Abstract—Surrogate-assisted evolutionary algorithms (SAEAs) have proven to be effective in solving computationally expensive optimization problems (EOPs). However, the performance of SAEAs heavily relies on the surrogate model and infill criterion used. To improve the generalization of SAEAs and enable them to solve a wide range of EOPs, this article proposes an SAEA called AutoSAEA, which features model and infill criterion auto-configuration. Specifically, AutoSAEA formulates model and infill criterion selection as a two-level multiarmed bandit problem (TL-MAB). The first and second levels cooperate in selecting the surrogate model and infill criterion, respectively. A two-level reward (TL-R) measures the value of the surrogate model and infill criterion, while a two-level upper confidence bound (TLUCB) selects the model and infill criterion in an online manner. Numerous experiments validate the superiority of AutoSAEA over some state-of-the-art SAEAs on complex benchmark problems and a real-world oil reservoir production optimization problem.
Index Terms—Auto algorithm design, expensive optimization, surrogate-assisted evolutionary algorithm (SAEA), two-level multiarmed bandit (TL-MAB).
I. INTRODUCTION
E
XPENSIVE optimization problems (EOPs) are prevalent in many engineering designs and applications (e.g., aerodynamic structures design [1], automobile crash analysis [2], and space tethered-net system design [3]). Unfortunately, solving such problems requires conducting computationally
Manuscript received 28 January 2023; revised 7 May 2023; accepted 27 June 2023. Date of publication 3 July 2023; date of current version 1 August 2024. This work was supported in part by the National Natural Science Foundation of China under Grant 62206120, Grant 62106096, and Grant 62036006; and in part by the Shenzhen Technology Plan under Grant JCYJ20220530113013031. (Lindong Xie and Genghui Li contributed equally to this work.) (Corresponding author: Zhenkun Wang.)
Lindong Xie and Genghui Li are with the School of System Design and Intelligent Manufacturing, Southern University of Science and Technology, Shenzhen 518055, China (e-mail: <EMAIL>; <EMAIL>). Zhenkun Wang is with the School of System Design and Intelligent Manufacturing and the Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China (e-mail: <EMAIL>). Laizhong Cui is with the College of Computer Science and Software Engineering and the Guangdong Laboratory of Artificial Intelligence and Digital Economy (SZ), Shenzhen University, Shenzhen 518060, China (e-mail: <EMAIL>). Maoguo Gong is with the Key Laboratory of Collaborative Intelligence Systems, Ministry of Education, Xidian University, Xi’an 710071, China (e-mail: <EMAIL>). This article has supplementary material provided by the authors and color versions of one or more figures available at https://doi.org/10.1109/ TEVC.2023.3291614. Digital Object Identifier 10.1109/TEVC.2023.3291614
expensive computer simulations or costly physics experiments since the analytical expressions of the problem are unavailable. Generally, the EOP can be expressed as follows:
min f (x)
s.t. xl ≤ x ≤ xu (1)
where x = (x1, . . . , xD) is the decision vector of D variables, xl = (xl,1, . . . , xl,D) and xu = (xu,1, . . . , xu,D) are the lower and upper bounds of the search space, respectively, and f (x) denotes a scalar objective function. We assume that the analytical expression of f (x) is unavailable and the calculation of f (x) is costly. Evolutionary algorithms have been shown to be popular and effective for solving black-box optimization problems [4], [5]. However, they often perform poorly on EOPs due to the large number of function evaluations (FEs) required in their optimization process. This limitation becomes more significant in the case of EOPs [6]. To address this issue, surrogate-assisted evolutionary algorithms (SAEAs) have been proposed. In SAEAs, the optimization is mainly driven by computationally cheap surrogate models. The popular surrogate models used in SAEAs include regression models, such as radial basis function (RBF) [7], Gaussian process model (GP) [8], and polynomial response surface (PRS) [9], and classification models, such as k-nearest neighbor (KNN) [10] and support vector machine model (SVM) [11]. Based on the adopted surrogate model, existing SAEAs can be roughly grouped into three categories: 1) single-model SAEAs [12], [13], [14], [15]; 2) multiplemodel SAEAs [16], [17], [18], [19], [20], [21]; and 3) adaptive-model SAEAs [22], [23], [24], [25]. Single-model SAEAs use a fixed surrogate model throughout the optimization process, typically combining a fixed infill criterion, such as GP with expected improvement (EI) [26] and lower confidence bound (LCB) [27], RBF with local search [28], or KNN with prescreening [29], to select candidate solutions for FEs. However, due to the limited training samples available and the inability of a single model to effectively fit various problem landscapes, single-model SAEAs often struggle to solve different EOPs effectively [18], [30]. In order to enhance the robustness and scalability of singlemodel SAEAs, multiple-model SAEAs have been developed, including hierarchical-model SAEAs and ensemble-model
1089-778X c© 2023 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


XIE et al.: SAEA 1115
SAEAs [31]. Hierarchical-model SAEAs commonly combine a global model and a local model [16], [18], [32], [33], [34]. The global model approximates the fitness landscape to search for promising regions, and the local model is used to conduct a refined search in the found local promising regions. Ensemble-model SAEAs typically train several different base surrogate models to cooperatively implement a more precise prediction [9], [19], [20], [21]. Generally, multiple-model SAEAs outperform single-model ones in most cases [15]. However, the computational cost of multiplemodel SAEAs is always higher compared to the single-model SAEAs [35], [36]. Additionally, some base models may not fit the problem landscape well, so their efficiency may still be low. To leverage well-established models effectively and reduce computational complexity, adaptive-model SAEAs have been proposed that automatically select the suitable model (or infill criterion) in an online manner [22], [24], [25]. However, the performance of such algorithms dramatically depends on the design of the adaptive selection strategy. It is well known that the surrogate model is tightly coupled with the infill criterion in SAEAs, and they cooperatively affect the performance of SAEAs. A model with different infill criteria can realize different balances of exploiting better solutions and exploring unexplored search regions. Naturally, a better balance between exploration and exploitation may be achieved by cooperatively considering the model and the infill criterion in a hierarchical coupled way. Unfortunately, existing adaptive-model SAEAs are either only for model or infill criterion selection but do not study adaptive selection for both cooperatively. Therefore, to improve the performance of SAEAs by filling this gap, this article proposes an SAEA (called AutoSAEA) with the model and infill criterion autoconfiguration by using the hierarchical multiarmed bandit (MAB) method. The contributions of this article are summarized as follows. 1) Formulating the surrogate model and infill criterion cooperative selection as a two-level MAB problem (TL-MAB) [37], [38], [39]. 2) Designing a two-level reward (TL-R) to measure the utility of the surrogate model and infill criterion in a cooperative manner. 3) Adopting a two-level upper confidence bound (TLUCB) to select the surrogate model and infill criterion cooperatively in an online manner. 4) Proposing an SAEA with a surrogate model and infill criterion auto-configuration, and verifying its advantages by comparing it with some state-of-the-art SAEAs on two sets of complex benchmark problems and a realworld problem. The remainder of this article is organized as follows. Section II reviews the related work. Section III introduces the background knowledge involved in the proposed algorithm. Section IV describes the proposed algorithm in detail. Section V presents the numerical experiments conducted to validate the effectiveness of the proposed algorithm. Finally, Section VI summarizes this article and discusses future work directions.
II. RELATED WORK
1) Single-Model SAEAs: Single-model SAEAs can either train a global model using all evaluated solutions or a local model with some good ones to predict the quality of new solutions. For example, the Bayesian optimization framework is a classic and popular single-model method [40], [41], [42]. It uses the GP model to build a surrogate for the objective and quantify the uncertainty in the surrogate. Then, it optimizes an acquisition function (e.g., EI [40] and LCB [43]) defined from the surrogate to decide the new solution for evaluation. GPEME [13] first constructs a local GP model in the lowerdimensional space, followed by the application of prescreening with LCB to determine which offspring can be selected for FE. CPS-MOEA [44] employs a local KNN classifier to filter out potentially nondominated solutions for real evaluation. SACOSO [45] evaluates the new position of the particle that has the minimum fitness value predicted by a global RBF model. In addition, SHPSO [46] and SAMSO [15] construct a local RBF model and a global RBF model, respectively, to select particles with predicted fitness values that are better than their personal best ones for FEs. MGP-SLPSO [14] formulates the approximated fitness and its corresponding uncertainty provided by a global GP model as a bi-objective problem and applies a nondominated sorting method to select the offspring for FEs. Furthermore, CA-LLSO [47] trains a local gradient boosting classifier to predict the level of the offspring produced by the level-based learning swarm optimizer. 2) Multiple-Model SAEAs: Multiple-model SAEAs mainly combine a global and a local model or train multiple models simultaneously to balance exploration and exploitation. For example, CAL-SAPSO [19] identifies the best and most uncertain solutions for FEs using a global ensemble surrogate model. Additionally, the optimum of the local ensemble model found by the local search is also used for expensive evaluation. HeE-MOEA [35] utilizes an SVM and two RBF models to build an ensemble model, which is combined with LCB and EI criteria to screen the offspring for expensive evaluations. ESAO [32] employs a global RBF to screen the offspring with the minimum predicted fitness value for FE. Besides, the optimum of the local RBF model found by the differential evolution (DE) is also evaluated. GSGA [33] adopts a local RBF-assisted trust-region method and a global GP-assisted genetic algorithm for exploitation and exploration, respectively. GL-SADE [18] trains a global RBF model and a local GP model to select offspring for FEs. Moreover, when the local GP model finds the current best solution, DE is applied further to search for its optimal solution. ESCO [21] constructs multiple RBF models on various low-dimensional sample sets, and then selects the models with superior performance to compose an ensemble surrogate. 3) Adaptive-Model SAEAs: Adaptive-model SAEAs always design an adaptive selection strategy to automatically choose a model or an infill criterion in an online manner. For example, GP-Hedge [22] adaptively selects an appropriate acquisition function from a portfolio of well-established ones, such as the probability of improvement, EI, and UCB. This selection is based on an online MAB strategy. Specifically,
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


1116 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024
the optimal solutions of all acquisition functions form a candidate solution pool, from which solutions are selected for FEs based on the cumulative rewards of their respective acquisition functions. While GP-Hedge only adaptively selects the acquisition functions, it fixes the surrogate model. Moreover, its computational efficiency is low since it needs to optimize multiple acquisition functions in each generation. ASMEA [30] first constructs a base model pool using GP, RBF, PRS, and linear Shepard interpolation. It then uses the prediction residual error sum of squares (PRESS) and root mean square error (RMSE) to select several meta-models from the base model pool for constructing five ensemble models, which are then included in the base model pool. Finally, a promising surrogate model is adaptively selected from the base model pool based on its minimum RMSE. While ASMEA only adaptively selects the models, it fixes the prescreening as the infill criterion. Moreover, its computational efficiency is low since it needs to conduct cross-validation for each base model. ESA [25] first constructs a pool of four different infill criteria, including DE evolutionary screening, surrogate-assisted local search, full-crossover strategy, and surrogate-assisted trust-region local search. Among them, the DE evolutionary screening strategy prefers exploration, the surrogate-assisted local search and trust-region local search strategies use different local search methods to favor exploitation, and the full-crossover strategy integrates good genes from historical solutions. Moreover, Q-learning is used to adjust the selection probability of each infill criterion through feedback information received during the optimization process. However, ESA fixes the RBF as the surrogate model. Additionally, some adaptive-model SAEAs have been proposed to deal with expensive multiobjective optimization problems. For instance, KTA2 [23] divides the optimization process into three states (i.e., convergence-demand, diversitydemand, and uncertainty-demand states) and uses the distances from solutions to the estimated ideal point and the pure diversity indicator to estimate the state. Moreover, the infill criterion is adaptively chosen based on the estimated optimization state, which guides the solution selection for FEs by taking into account the requirements on convergence, diversity, and model uncertainty separately. RVMM [48] uses the GP as the surrogate model and develops an adaptive model management strategy to adaptively choose the convergence-related criterion and diversity-related criterion. The adaptive model management strategy is assisted by two sets of reference vectors. One set of adaptive reference vectors focuses on convergence, while the other set of fixed reference vectors concentrates on diversity. The GP and RBF models are the most popular surrogate models due to their high fidelity and simplicity. Generally, the RBF model is computationally more efficient than the GP model. However, the GP model can provide uncertain information about its predictions. Therefore, to take advantage of the GP and RBF models in dealing with different problems, IBEAMS [24] designs an acceptable reliability tolerance criterion to adaptively determine whether to use the GP model or the RBF model in environmental selection. Specifically, when the uncertainty of the GP model exceeds the acceptable error, the RBF model is used. Otherwise, the GP models are used.
III. BACKGROUND
A. Surrogate Models
1) GP Model: Given a training data set {xi, f (xi)}N
i=1, GP
predicts the output fGP(x) of a solution x in the way of: fGP(x) = μ(x) + (x), where μ(x) represents a global trend of the training data, and (x) ∼ N (0, σ 2(x)) is a normal distribution. μ(x) and σ 2(x) are estimated as follows [40]:
μ(x) = k(x) K−1F (2)
σ 2(x) = κ(x) − k(x) K−1k(x) (3)
where k(x) = [C(x, x1), . . . , C(x, xN)] ; K is an N × N matrix and Ki,j = C(xi, xj); κ(x) = C(x, x), and F = [f (x1), . . . , f (xN)] . The covariance function C(·, ·) can be commonly calculated as follows:
C(xi, xj
) = exp
(
−
D ∑
d=1
θd
∣∣xi,d − xj,d
∣∣2
)
(4)
where the hyper-parameters θ can be obtained by maximizing the likelihood function [33]. Finally, for an unknown solution x, its predicted fitness value fˆGP(x) and uncertainty sˆ(x) are given in the following:
fˆGP(x) = μ(x) + k(x) K−1(F − Iμ(x)) (5)
sˆ2(x) = σ 2(x)
[
1 − k(x) K−1k(x) +
(1 − I K−1k(x))2
I K−1I
]
(6)
where I is an N × 1 unit vector, and ˆs(x) = √ˆs2(x) is the RMSE. 2) RBF Model: The RBF model uses a linear combination of basis functions to approximate the fitness landscape. For the given training data set {xi, f (xi)}N
i=1, the form of the RBF model can be formulated as follows [49]:
fˆRBF(x) =
N ∑
i=1
wiφ(‖x − xi‖) + b0 +
D ∑
j=1
bjxj (7)
where w = (w1, w2, . . . , wN) is the weight vector of the basis function, and this article adopts the cubic function φ(‖x−xi‖) = (‖x−xi‖)3, where ‖·‖ denotes the Euclidean distance. b = (b0, b1, . . . , bD) is the coefficient of the first-order polynomial. The parameters w and b in (7) can be obtained as follows:
[w
b
] =
[P
P 0(D+1)×(D+1)
]†[ F
0(D+1)×1
]
(8)
where i,j = φ(‖xi − xj‖2), i, j = 1, 2, . . . , N; P = [P1, . . . , PN ] and Pi = [1, xi,1, . . . , xi,D] ; 0(D+1)×(D+1) is a zero matrix of (D + 1) × (D + 1), and † is the generalized inverse, and F = [f (x1), . . . , f (xN)] . 3) PRS Model: The commonly used second-order polynomial is defined as follows [9]:
fˆPRS(x) = β0 +
∑D
i=1
βixi +
D ∑
i=1
D ∑
j≥i
βijxixj (9)
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


XIE et al.: SAEA 1117
where coefficients β0, βi, and βij are regression parameters for the intercept, the linear term, and the quadratic term, respectively. For the given training data set {xi, f (xi)}N
i=1, the unknown
coefficients of the above polynomial model β = (β0, . . . , βD, β1,1, . . . , β1,D, β2,3, . . . , β2,D, . . . , βD−1,D, βD,D) can be obtained by the least square method as follows [50]:
β=
(
PP
)−1
P F (10)
where P = [P1, . . . , PN ] , Pi = [1, xi,1, . . . , xi,D] , and F = [f (x1), . . . , f (xN)] .
4) KNN Model: KNN is a popular classifier [29], [44]. Based on a training data set {xi, f (xi), y(xi)}N
i=1, where f (xi)
and y(xi) denote the objective function value and class/level of the solution xi, respectively. For a new solution x, its class/level is predicted as follows:
yKNN(x) = mode({y(x1), . . . , y(xK)}) (11)
where x1, . . . , xK are K nearest neighbors of x, and mode() denotes the mode of a set. In this article, K is set to 1 [29], [44], which means that the new solution x is assigned the same class as its nearest neighbor. In this article, all surrogate models are trained based on the current population P in each iteration.
B. Infill Criteria
Since the surrogate models are tightly coupled with the infill criteria in SAEAs, the corresponding infill criteria for the surrogate models mentioned in the previous section are introduced as follows.
1) Infill Criteria for the GP Model: The LCB and EI criteria are commonly combined with GP for determining new solutions for FEs, which are expressed sequentially as follows [13], [33]:
x∗ = arg min x∈X
(fˆGP(x) − wsˆ(x)
)
(12)
where w is set to 2 in this article [13], [27]. X denotes a set of unknown solutions.
x∗ = arg max x∈X
((
fmin − fˆGP(x)
)
(Z) + ˆs(x)φ(Z)
)
(13)
where Z = ([fmin − fˆGP(x)]/[sˆ(x)]), fmin is the current minimum objective function value, and (·) and φ(·) are the density and cumulative distribution functions of standard normal distribution, respectively. This article adopts the DE mutation and crossover operators to generate the new solution set X = {o1, . . . , oN}, where N is the population size. To be specific, assume that the current population is P, oi = (oi,1, . . . , oi,D) is generated as follows:
vi = xi + F × (xb − xi) + F × (xr1 − xr2) (14)
oi,j =
{ vi,j, if randi,j ≤ CR or j = jrand
xi,j, otherwise (15)
where xi is the ith solution in P, xr1 and xr2 are randomly selected from the current population P, xb is the best solution of P; F is the scale factor, and it is set to 0.5 in this
article. randi,j and jrand are randomly selected from [0, 1] and {1, . . . , D}, respectively, and CR ∈ [0, 1] denotes the crossover rate, which is set to 0.9 in this article. Note that if oi,j violates the boundary constraints, it will be repaired as follows:
oi,j = xl,j + rand × (xu,j − xl,j
). (16)
2) Infill Criteria for the RBF and PRS Models: For all regression models (e.g., RBF and PRS), prescreening and local search are widely used in SAEAs, which are formulated as follows [28], [51]:
x∗ = arg min x∈X
fˆRBF/PRS(x) (17)
where x and fˆRBF/PRS(x) denote the unknown solution and its predicted fitness value by RBF/PRS, respectively. It should be noted that if X is a finite set of solutions, (17) belongs to prescreening, and it denotes a local search if X is a subspace of the search space. In this article, the above DE operator is used to generate X for the prescreening. For the local search, X is defined as follows:
X = [lb, ub] (18)
where lb = (l1, . . . , lD) and ub = (u1, . . . , uD) , lj = min{xi,j, xi ∈ P}, and uj = max{xi,j, xi ∈ P}, j = 1, . . . , D. Moreover, DE is used to solve (17) in the local search in this article.
3) Infill Criteria for the KNN Model: Classification models are often used to predict the level of the unknown solutions [52]. To this end, the current population is evenly divided into L levels P1, . . . , PL based on the fitness, and the best (N/L) solutions belong to the first level P1. Two infill criteria (e.g., L1-exploitation and L1-exploration) have been designed for the classifier model. They are defined as follows [47]. 1) L1-Exploitation: The L1-exploitation criterion is defined as
x∗ = arg min
x∈X 1 max
{
‖x − p‖, p ∈ P1}
. (19)
2) L1-Exploration: The L1-exploration is defined as
x∗ = arg max
x∈X 1 min
{
‖x − p‖, p ∈ P1}
(20)
where X 1 includes the solution in new generated solution set X whose predicted level by the KNN model is L1. In this article, X is also generated by the above-described DE operators, and the number of levels is set to 5. For the models and infill criteria discussed above, we have the following remarks. Over the past two decades, a variety of regression and classification models have been employed to aid evolutionary algorithms in solving complex optimization problems. As far as our knowledge goes, GP, RBF, PRS, and KNN are the most effective and commonly used models in state-of-the-art SAEAs [9], [19], [44], each with unique advantages in handling diverse problems. In this article, our goal is to develop an SAEA with auto-configuration of models and infill criteria. Therefore, we have chosen these well-known models for their established strengths. However, it should be
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


1118 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024
(a) (b)
Fig. 1. Illustrative examples of HMAB clustering structures. (a) Disjoint clustering structure. (b) Hierarchical clustering structure.
noted that other models and infill criteria can also be easily incorporated into our proposed algorithm. Moreover, regarding the parameters involved in the infill criteria, we have tested several representative values for each parameter in our preliminary experiments. The experimental results show that they do not have a significant influence. Therefore, we use commonly accepted settings for these parameters in this article.
C. Hierarchical Multiarmed Bandit
MAB is a powerful tool for online learning [37]. In the standard MAB problem, the player must pick an arm at from a set of arms A at each time slot t and then obtain a reward rat , which is produced from a distribution that is unknown to the player. The performance of an algorithm in MAB is typically measured by the regret, and the player’s goal is to minimize the expected cumulative regret over a sequence of T time slots. The expected cumulative regret can be expressed as follows [37]:
E[RT ] =
T ∑
t=1
rat∗ − rat (21)
where at∗ denotes the unknown best arm at the t-th time slot. Hierarchical MAB (HMAB) is an extension of the standard MAB. Two commonly used hierarchical structures have been developed [39].
1) Disjoint Clustering Structure: The K arms are classified into a set of clusters, and each arm a ∈ A belongs to only one cluster. The player first picks a cluster and then chooses an arm in the selected cluster. Finally, a reward is produced by the chosen arm. An illustrative example of disjoint clustering is shown in Fig. 1(a).
2) Hierarchical Clustering Structure: The arms can be divided into multiple levels. The arms on the same level are different from each other, and the different highlevel arms can be associated with the same low-level arms. The player first selects an arm from the high-level and then chooses an arm from its associated low-level. Finally, a reward is returned by all chosen arms cooperatively. An illustrative example of hierarchical clustering is shown in Fig. 1(b). Many theories and experiments have demonstrated that HMAB has significant advantages over standard MAB [37], [38], [39], [53], [54], [55]. In general, its benefits can be summarized into three aspects: 1) the regret bound of HMAB is lower than that of standard MAB; 2) HMAB can reduce the size of the arm space, especially when the arm space is large;
Algorithm 1: AutoSAEA(N, MaxFEs, AH, AL, α). 1: Input:
2: Population size: N 3: Maximum number of FEs: MaxFEs 4: High-level arm set: AH = {aH
1 , . . . , aH
4}
5: Low-level arm set: AL = {aL
1 , . . . , aL
8}
6: Parameters: α 7: Initialization:
8: Use LHS to sample N solutions in the search space 9: Evaluate and save them into the database D 10: Set QaH(1), a ∈ AH and QaL, a ∈ AL to 0
11: Set TaH(1), a ∈ AH and TaL, a ∈ AL to 0
12: Set CA based on AH and AL 13: Set FEs = N and t = 1 14: while FEs < MaxFEs do
15: Select N best solutions from D as the population P 16: if t ≤ |CA| then
17: Select the t-th combinatorial arm in CA 18: Set atH and atL as the first and second arm in the selected combinatorial arm, respectively 19: else 20: Choose atH and atL by TL-UCB 21: end if
22: Obtain xt by atH and atL cooperatively 23: Evaluate xt 24: D = D ∪ xt
25: FEs = FEs + 1
26: TH
atH
(t + 1) = TH
atH
(t) + 1, TL
atL
(t + 1) = TL
atL
(t) + 1
27: Update the value of atH and atL by TL-R 28: t = t + 1 29: end while
30: Output: The best solution in D
and 3) HMAB can greatly mitigate the chances of selecting suboptimal arms, leading to improved performance in solving EOPs. Therefore, we propose integrating HMAB into SAEAs to enable the adaptive and cooperative selection of surrogate models and infill criteria. In this article, we consider the hierarchical clustering structure of HMAB, and its details will be explained in the following section.
IV. AUTOSAEA
A. Algorithm Structure
The framework of the proposed AutoSAEA is shown in Algorithm 1. Its input includes the population size N (line 2), the maximum number of FEs MaxFEs (line 3), a set of highlevel arms AH (line 4), a set of low-level arms AL (line 5), and the parameter α (line 6). The high-level and low-level arms denote the surrogate model and the infill criterion, respectively. In this article, we set AH = {aH
1 = GP, aH
2 = RBF, aH
3=
PRS, aH
4 = KNN} and AL = {aL
1 = LCB, aL
2 = EI, aL
3=
prescreening, aL
4 = local search, aL
5 = prescreening, aL
6=
local search, aL
7 = L1-exploitation, aL
8 = L1-exploration}.1
1Note that aL
3 and aL
4 are associated with RBF, and aL
5 and aL
6 are associated
with PRS.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


XIE et al.: SAEA 1119
In the initialization (lines 7–13), an initial population with N solutions is generated by the Latin hypercube sampling (LHS) method (line 8). These solutions are evaluated directly and saved into a database D (line 9). The value for each arm and the number of selections for each arm is set to 0 (lines 10 and 11). Moreover, to make each arm be selected at least once, a legal combinatorial arm set is set as CA = {(GP, LCB), (GP, EI), (RBF, prescreening), (RBF, local search), (PRS, prescreening), (PRS, local search), (KNN, L1exploitation), (KNN, L1-exploration)} (line 12). Finally, the current number of FEs and the iteration number are set to N and 1, respectively (line 13). In the optimization process (lines 14–29), it first selects the N best solutions from D as the population P (line 15). If the number of iterations t is less than the cardinality of the combinatorial arm set CA (line 16), the t-th combinatorial arm in CA is chosen (line 17). Then, the high-level arm atH and
low-level arm atL are set to the first and second arms in the selected combinatorial arm (line 18), respectively. Otherwise, they are determined by the TL-UCB method (line 20). When the high-level arm atH and low-level arm atL are selected, a new solution xt is determined by them cooperatively (line 22). To be specific: 1) when atH represents the GP model, an offspring population with N solutions is first generated by DE operators. Then, the offspring solution with the best LCB value (if atL = LCB) or best EI value (if atL = EI) is chosen as xt;
2) when atH represents the RBF model or
PRS model, if atL = prescreening, an offspring population with N solutions is first generated by DE operators. Then, the offspring solution with the best fˆRBF(x) value (if atH = RBF) or fˆPRS(x) value (if
atH = PRS) is chosen as xt. If atL = local search, DE
is used to optimize fˆRBF(x) (if atH = RBF) or fˆPRS(x)
(if atH = PRS) to get xt. Note that in this article, the DE local search is conducted with a population size of N and a maximum number of 100D + 1000 generations [25]; 3) when atH represents the KNN model, an offspring population with N solutions is first generated using DE operators. Then, xt is selected based on L1-exploitation (if atL = L1-exploitation) or L1-exploration (if atL = L1-exploration) from these offspring solutions. Then, xt is evaluated and added into the database D (lines 23 and 24), and the FEs is increased by 1 (line 25). Finally, the number of selections for atH and atL is increased by 1 (line 26), and their values are updated by the TL-R method (line 27). When the maximum computational budget is consumed, the best solution in the database D is output as the final solution (line 30). In the following, we will give a detailed introduction to the two core components, TL-UCB and TL-R, sequentially.
B. TL-UCB
TL-UCB is the policy to select the high-level arm (surrogate model) and low-level arm (infill criterion). To choose a suitable high-level arm, the high-level UCB is executed, which
Algorithm 2: (atH, atL) = TL-UCB(AH, AL, t, TH, TL, QH(t),
QL(t), α). 1: Input:
2: High-level arms set: AH = {aH
1 , . . . , aH
4}
3: Low-level arm set: AL = {aL
1 , . . . , aL
8} 4: Current number of iterations: t 5: Number of selection for each arm: TH(t) and TL(t) 6: The value for each arm: QH(t) and QL(t) 7: Parameter: α 8: Set AL
aH
1
= {aL
1 , aL
2 }, AL
aH
2
= {aL
3 , aL
4 },
AL
aH
3
= {aL
5 , aL
6 }, AL
aH
4
= {aL
7 , aL
8}
9: atH = arg max
a∈AH
[
QaH(t) +
√ α ln(t) TaH (t)
]
10: atL = arg max
a∈AL
atH
[
QaL(t) +
√ α ln(t) TaL (t)
]
11: Output: High-level arm atH and low-level arm atL
Fig. 2. Association relationship between the high-level arms and low-level ones.
is formulated as follows:
aH
t = arg max
a∈AH
[
QH
a (t) +
√
α ln(t) TaH (t)
]
(22)
where QaH(t) denotes the value of the high-level arm a at the
t-th iteration and TaH(t) is the number of selections of the high-level arm a during the past t − 1 iterations, t is the current iteration number, and α is a control parameter used to balance the tradeoff between exploiting well-performing arms and exploring rarely selected arms. When the high-level arm is determined, the low-level arm is picked up by the low-level UCB, which is defined as follows:
aL
t = arg max
a∈AL
atH
[
QL
a(t) +
√
α ln(t) TaL(t)
]
(23)
where QaL(t) denotes the value of the low-level arm a in
the t-th iteration, TaL(t) is the number of selections of the low-level arm a during the past t − 1 iterations. Here, it should be noted that not each high-level arm can be associated with all low-level ones. Therefore, AL
atH
denotes the low-level arms that can be associated with the highlevel arm atH. In this article, the association relationship between the high-level arms and low-level arms is shown in Fig. 2. Moreover, the pseudo-code of the TL-UCB is provided in Algorithm 2.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


1120 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024
C. TL-R
After determining the high-level arm (surrogate model) and low-level arm (infill criterion), the corresponding surrogate model is trained based on the current population P. Then, a new solution xt is obtained by the selected high-level and lowlevel arms cooperatively. To measure the optimization utility of the chosen atL and atH, a TL-R is designed as follows. 1) Low-Level Reward:
ratL = − 1
N I(xt) + N + 1
N (24)
where N is the population size and I(xt) denotes the ranking of xt in the current population P. Specifically, if xt is the current best solution, I(xt) = 1 such that ratL = 1. On the contrary,
if I(xt) = N + 1 such that ratL = 0. The low-level reward ratL
has four characteristics: 1) ratL is bounded in [0, 1]; 2) ratL is
linearly proportional to the ranking I(xt) of the newly generated solution xt, the better the ranking, the larger the reward; 3) the low-level rewards are nonsparse; and 4) it remains stationary to some extent throughout the entire optimization process. Based on the low-level reward ratL , the value of atL is updated
as follows:
QL
atL (t + 1) =
TL
atL
(t)QL
atL
(t) + ratL
TL
atL
(t) + 1 . (25)
It should be noted that the value of the high-level arm atH is increased if and only if the low-level reward is larger than its current value [e.g., ratL ≥ QL
atL
(t)].
2) High-Level Reward: After the value QL
atL
of the low-level
arm atL is updated, the reward for the high-level arm atH is designed as follows:
ratH =
QL
atL
(t + 1) − QL
atL
(t)
|AL
atH
| (26)
where AL
atH
denotes the low-level arm set that is associated
with the high-level arm atH. Clearly, the high-level reward ratH
can be positive or negative. To be specific, if the value of the low-level arm atL increases (e.g., QL
atL
(t + 1) − QL
atL
(t) ≥ 0), the reward of the high-level arm is positive. Otherwise, it is negative. Based on the high-level reward ratH , the value of atH is
updated as follows:
QH
atH (t + 1) = QH
atH (t) + ratH . (27)
Clearly, if the high-level reward of atH is positive, its value will be increased. Otherwise, its value will be decreased. It should be pointed out that the value QH
atH
(t + 1) of atH is the mean of the value of its associated low-level arms. To be specific, putting (26) into (27), we have
QH
atH (t + 1) =
|AL
atH
| · QH
atH
(t) + QL
atL
(t + 1) − QL
atL
(t)
|AL
atH
| . (28)
Fig. 3. Numerical example of reward propagation in TL-R.
Algorithm 3: (QH
atH
(t+1), QL
atL
(t+1)) = TL-R(N, P, xt, TL
atL
(t),
QL
atL
(t), QH
atH
(t)).
1: Input:
2: Population size: N 3: Population: P 4: New generated solution: xt 5: Number of selections of atL: TL
atL
(t)
6: The value of atL and atH: QL
atL
(t) and QH
atH
(t)
7: Calculate the low-level reward ratL of atL as (24)
8: Calculate QL
atL
(t + 1) of atL as (25)
9: Calculate the high-level reward ratH of atH as (26)
10: Calculate QH
atH
(t + 1) of atH as (27)
11: Output: The updated values QH
atH
(t + 1) and QL
atL
(t + 1) of
atH and atL
Assume that |AL
atH
| · QH
atH
(t) = ∑
aL ∈AL
atH
QL
aL (t) holds for the
t-th generation. Therefore
QH
atH (t + 1) =
∑
aL ∈AL
atH
QL
aL (t) + QL
atL
(t + 1) − QL
atL
(t)
|AL
atH
| . (29)
Since the value of the un-selected low-level arm is not updated, QL
aL (t + 1) = QL
aL (t), (aL = atL). Finally, we have
QH
atH (t + 1) =
∑
aL ∈AL
atH
QL
aL (t + 1)
|AL
atH
| . (30)
Since the values of both the low-level arm and high-level arm are initialized to 0, the equation |AL
atH
| · QH
atH
(t) =
∑
aL ∈AL
atH
QL
aL (t) holds in the initialization (i.e., t = 1).
Therefore, in each generation, the value of aH
t+1 is exactly the mean of the values of its associated low-level arms. The pseudocode of TL-R is provided in Algorithm 3. In addition, Fig. 3 illustrates a numerical example of reward propagation in TL-R. In this figure, we assume that at the t-th generation, TL-UCB selects the high-level arm atH and
low-level arm atL, and generates a solution xt by them cooperatively. Suppose the low-level reward is ratL = 0.8, as
computed using (24), and the value of the low-level arm is
QL
atL
(t + 1) = 0.6, as obtained from (25). Then, using (26), we can compute the reward of the high-level arm ratH = 0.05, and
finally, the value of the high-level arm QH
atH
(t + 1) = 0.7 is determined using (27). The path of the reward is indicated by solid red lines.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


XIE et al.: SAEA 1121
Compared with other existing adaptive-model SAEAs [22], [23], [24], [25], the proposed AutoSAEA has the following differences. 1) Although the model and infill criterion configuration are common, they differ from those used in the existing adaptive-model SAEAs. 2) AutoSAEA can adaptively choose the surrogate model and infill criterion in an online manner. However, existing adaptive-model SAEAs are either only for model selection or infill criterion selection but do not study adaptive selection for both cooperatively. 3) AutoSAEA formulates the surrogate model and infill criterion selection as a TL-MAB. Moreover, a TL-R and a TL-UCB designed to effectively capture good coupling behavior between the surrogate model and infill criterion. The adopted online learning strategy of AutoSAEA is obviously different from those of the existing adaptive SAEAs.
V. NUMERICAL EXPERIMENTS
A. Benchmark Problems
Commonly used benchmark problems to evaluate the performance of the SAEAs are the Ellipsoid function, Rosenbrock function, Ackley function, Griewank function, Rastrigin function, and a few problems from CEC2005 competition [14], [15], [16], [18], [19], [32], [47], [56]. However, most of these problems are simple and hardly represent the complex characteristics of the real-world EOPs. Therefore, we first adopt CEC2005 [56] and CEC2015 problems [57] with D = 10 and D = 30 to evaluate the performance of our proposed AutoSAEA. Moreover, performance is measured using the function error metric f (x∗)−f (xo), where x∗ denotes the best solution found by the algorithm, and xo is the real optimum. To analyze the significant difference between the results obtained by all test algorithms, the Wilcoxon rank-sum test and Friedman test with the Hommel post-hoc procedure are conducted at a significant level of 0.05 [58], [59]. The symbols “+,” “=,” and “−” denote that AutoSAEA is statistically better than, competitive with, and worse than the compared algorithm, respectively.
B. Comparison With Existing SAEAs
In this section, comparison experiments are conducted between AutoSAEA and two traditional Bayesian optimization methods (i.e., GP-LCB [43] and GP-EI [40]) and eight state-of-the-art SAEAs (i.e., IKAEA (2021) [42], ESAO (2019) [32], CA-LLSO (2020) [47], TS-DDEO (2021) [31], SA-MPSO (2021) [16], SAMFEO (2022) [58], GL-SADE (2022) [18], and ESA (2022) [25]). The parameters of all compared algorithms remain the same as in their original papers. For the proposed AutoSAEA, parameters N and α are set to 100 and 2.5, respectively. The statistical comparison results are obtained under 1000 FEs and 20 independent runs [15], [16], [18], [32], [47] on each problem. The mean and standard deviation of the obtained function error values for each algorithm on the CEC2005 10D problems, CEC2005 30D problems, CEC2015 10D problems, and CEC2015 30D problems are
provided in Tables I–IV of the supplementary file, respectively, and the statistical results are shown in Table I. For the CEC2005 10D problems, AutoSAEA exhibits significant advantages over all compared algorithms. Specifically, it outperforms GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 8, 9, 12, 13, 10, 14, 11, 11, 14, and 9 out of 15 problems, respectively. Moreover, it is only worse than CA-LLSO on 1 out of 15 problems. Based on the Friedman test, AutoSAEA ranks first and significantly outperforms all compared algorithms except GP-LCB and ESA. For the CEC2005 30D problems, AutoSAEA is significantly better than all compared algorithms in most cases. It outperforms GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TSDDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 7, 8, 12, 11, 10, 9, 12, 8, 12, and 8 out of 15 problems, respectively. Moreover, it is only worse than GP-LCB, GP-EI, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 2, 2, 1, 1, 2, 1, 4, 2, and 3 out of 15 problems. AutoSAEA takes first place and is significantly better than all compared algorithms except GP-LCB, GP-EI, SAMFEO, and ESA according to the Friedman test results. For the CEC2015 10D problems, AutoSAEA achieves the best results in most cases. Specifically, it is significantly better than or at least as good as TS-DDEO and ESAO in all cases. Additionally, it is only worse than GP-LCB, GP-EI, IKAEA, CA-LLSO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 2, 2, 1, 1, 1, 2, 2, and 2 out of 15 problems, respectively. Based on the statistical results of the Friedman test, AutoSAEA is the champion and significantly outperforms all compared SAEAs except GP-LCB, GP-EI, SAMFEO, and ESA. For the CEC2015 30D problems, AutoSAEA is still the winner in the overall comparison of algorithms. It is significantly better than GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GL-SADE, and ESA on 5, 6, 12, 12, 9, 9, 12, 6, 12, and 6 out of 15 cases, respectively. Moreover, it is only worse than GP-LCB, GP-EI, IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, SAMFEO, GLSADE, and ESA on 2, 3, 1, 0, 3, 2, 1, 1, 1, and 1 out of 15 problems, respectively. According to the statistical results of the Friedman test, AutoSAEA ranks first and is significantly better than IKAEA, ESAO, CA-LLSO, TS-DDEO, SA-MPSO, and GL-SADE. To evaluate the performance of SAEAs, it is necessary to test them on various problems with different characteristics. It is well known that different models and infill criteria are suitable for solving different problems [23], [24], [25], [30]. The proposed AutoSAEA does not focus on developing new models or infill criteria but aims to leverage the existing well-established ones to improve the performance of SAEA. Therefore, although the surrogate model and infill criterion adopted in AutoSAEA are common configurations, the reason why it performs better than other state-of-the-art SAEAs is explained as follows.
1) Compared With Single-Model SAEAs: Single-model SAEAs use a fixed surrogate model and infill criterion for all problems. While these algorithms can solve specific problems well, they often perform poorly on
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


1122 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024
TABLE I COMPARISON RESULTS BETWEEN AUTOSAEA AND ITS COMPETITORS AND THE RANKING OF EACH ALGORITHM ON CEC2005 PROBLEMS AND CEC2015 PROBLEMS WITH D = 10 AND D = 30
others. Therefore, when evaluating their performance on a sufficiently diverse set of problems, single-model SAEAs tend to perform worse than multiple-model SAEAs and adaptive-model SAEAs [18], [19], [35].
2) Compared With Multiple-Model SAEAs: While multiplemodel SAEAs can leverage the advantages of different models and infill criteria, they cannot identify which ones are most suitable for a given problem. This often leads to high consumption of computational resources on ineffective models and infill criteria [23].
3) Compared With Adaptive-Model SAEAs: Adaptivemodel SAEAs aim to not only exploit the advantages of different models and infill criteria but also identify the most suitable ones for a given problem. However, the performance of adaptive SAEAs depends on the specific model and infill criterion configuration used, as well as the adaptive selection strategy. Compared to existing adaptive-model SAEAs, AutoSAEA not only enriches the model/infill criterion configuration but also adopts a more effective adaptive selection strategy (i.e., TL-MAB) to select them in a hierarchical, coupled way. Therefore, AutoSAEA can outperform existing state-of-the-art adaptive-model SAEAs. Due to page limitations, we have included the convergence profiles of all tested algorithms, as well as the computational complexity of AutoSAEA and running times of all compared algorithms, in the supplementary file.
C. Adaptive Behavior Analysis
AutoSAEA formulates the surrogate model and infill criterion selection as a TL-MAB problem and designs a TL-R and a TL-UCB to address it. To demonstrate the effectiveness of the adaptive surrogate model and infill criterion selection used in AutoSAEA, we first compare it with eleven variants. 1) V-CA1 to V-CA8: AutoSAEA uses the fixed combinatorial arms in CA during the entire optimization process. V-CAi denotes that it uses the ith (i = 1, . . . , 8) combinatorial arm.
2) V-Random: The surrogate model and infill criterion are randomly selected. 3) V-SLUCB: AutoSAEA uses a single-level UCB and a single-level reward to adaptively choose the combinatorial arms from CA. 4) V-Q: AutoSAEA uses the Q-learning method [25] to adaptively select the combinatorial arms from CA in each iteration. The experiments are conducted on the CEC2005 10D and 30D problems, with all experimental setups kept the same as in the previous section. The mean and standard deviation of the function error value for each variant are provided in Tables V and VI of the supplementary file, and the statistical results are shown in Table II. For the CEC2005 10D problems, the Wilcoxon rank-sum test indicates that AutoSAEA significantly outperforms its variants. Specifically, it outperforms or competes with all variants on all problems, except that it is worse than VCA-5 and V-Q on one problem each out of 15. Overall, AutoSAEA achieves the best ranking and significantly outperforms VCA2, VCA-4, VCA-5, VCA-6, VCA-7, and VCA-8 according to the Friedman test. For the CEC2005 30D problems, based on the Wilcoxon rank-sum test, AutoSAEA is significantly better than all variants in most cases, and it is only outperformed by V-CA1, V-CA2, VCA-5, V-CA7, V-Random, and V-Q on 2, 2, 2, 2, 2, and 2 out of 15 problems, respectively. While AutoSAEA exhibits remarkable advantages over some of its variants, such as V-CA4, V-CA5, V-CA6, V-CA7, and V-CA8, it is worth noting that there is no significant difference between AutoSAEA and some of its variants, namely, V-CA1, V-CA2, V-CA3, V-Random, V-SLUCB, and V-Q. This indicates that AutoSAEA does not have advantages over its variants on some problems. The reasons behind this are provided as follows, based on the detailed analysis of the results. 1) For some problems, such as CEC2005 F8 (D = 10), CEC2005 F15 (D = 10), CEC2005 F8 (D = 30), CEC2005 F11 (D = 30), and CEC2005 F14 (D = 30), no models and infill criteria work effectively, so AutoSAEA cannot address them well.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


XIE et al.: SAEA 1123
TABLE II COMPARISON RESULTS BETWEEN AUTOSAEA AND ITS VARIANTS AND THEIR RANKING ON CEC2005 10D AND 30D PROBLEMS
2) For some problems, such as CEC2005 F9 (D = 10), CEC2005 F10 (D = 10), CEC2005 F12 (D = 10), CEC2005 F2 (D = 30), and CEC2005 F3 (D = 30), they can be addressed well by various models and infill criteria, so AutoSAEA does not have significant advantages over its some variants on these problems. To visually demonstrate the adaptive behavior of our proposed AutoSAEA, we show the selected high-level arm (surrogate model) and low-level arm (infill criterion) during the optimization process of AutoSAEA in solving four representative problems, namely, CEC2005 F4 (D = 10), CEC2005 F10 (D = 10), CEC2005 F2 (D = 30), and CEC2005 F12 (D = 30), in Fig. 4. Additionally, we count the number of times each surrogate model and infill criterion is chosen in Fig. 5. From Figs. 4 and 5, we observe the following. 1) For CEC2005 F4 (D = 10), the PRS with prescreening is selected most frequently. This is because this problem is unimodal, its landscape is smooth, and its dimensionality is small, making PRS a good choice for approximating this problem. 2) For CEC2005 F10 (D = 10), the algorithm prefers different models at different optimization stages. Specifically, the whole optimization procedure can be roughly divided into five stages, and GP, KNN, RBF, PRS, and KNN are, respectively, selected most frequently. 3) For CEC2005 F2 (D = 30), the GP model with EI and LCB is selected most frequently, especially in later stages. Interestingly, GP always combines LCB when 650 < FEs < 750. 4) For CEC2005 F12 (D = 30), GP with LCB or EI is mainly chosen when 100 < FEs < 350. However, when 350 < FEs < 550, the RBF model with prescreening or local search is preferred, and the GP model is no longer selected. When 550 < FEs < 1000, RBF and GP are the most and second most commonly chosen models, respectively. In summary, for different problems and different optimization stages, the selected surrogate model and infill criterion are different. Therefore, combined with the outstanding performance, the effectiveness of the surrogate model and infill criterion auto-configuration in AutoSAEA has been verified.
D. Oil Reservoir Production Optimization Problem
AutoSAEA is applied to an oil reservoir production problem in the section to demonstrate its advantage further. This
TABLE III RESULT OF GP-LCB, GP-EI, IKAEA, ESAO, TS-DDEO, SA-MPSO, GL-SADE, ESA, AND AUTOSAEA ON THE OIL RESERVOIR PRODUCTION OPTIMIZATION PROBLEM. THE UNIT OF TIME IS SECONDS
production optimization problem aims to find the optimal control parameters for the water-injection-rate of injection wells and fluid-production-rate of production wells in the expected life to maximize the net present value (NPV). In general, the oil reservoir production optimization problem can be expressed in the following form [60]:
max
T ∑
t=1
troQo,t(x) − rwQw,t(x) − riQi,t(x)
s.t. 0 ≤ xi,t ≤ 500, i = 1, . . . , 8, t = 1, . . . , 5 (31)
where x = (x1,1, . . . , x1,5, . . . , x8,1, . . . , x8,5) is the decision vector, and xi,t denotes the flow rate of the ith well at the t-th time step. Therefore, 40 variables need to be optimized. The lower and upper bounds of each decision variable are set to 0 STB/day and 500 STB/day, respectively. T = 5 denotes the number of time steps. t = 720 is the time of the t-th time step. ro, rw, and ri, respectively, denote the oilproduction revenue, water-production cost, and water-injection cost, which are set to 20 USD/STB, 1 USD/STB, and 3 USD/STB, respectively. Qo,t, Qw,t, and Qi,t denote the oilproduction rate, water-production rate, and injection-flow-rate at the t-th time step, respectively, which are calculated by a numerical simulator. In this case, the Egg model [61], including eight water-injection wells and four production wells, is selected as the reservoir simulator. More information about the Egg model can be found in [61]. In this article, the MRST toolbox [62] is adopted to conduct the Egg model. Note that it takes about 40 s to evaluate one solution using the simulator. To demonstrate the performance of AutoSAEA in solving the oil reservoir production optimization problem, eight existing SAEAs are used to test. To make a fair comparison, the initial number of solutions and the total number of FEs for all algorithms are set to 100 and
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


1124 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024
Fig. 4. Selected surrogate model and infill criterion during the optimization process of AutoSAEA on CEC2005 F4 (D = 10), CEC2005 F10 (D = 10), CEC2005 F2 (D = 30), and CEC2005 F12 (D = 30).
Fig. 5. Number of times each surrogate model and infill criterion is chosen during the optimization process of AutoSAEA on CEC2005 F4 (D = 10), CEC2005 F10 (D = 10), CEC2005 F2 (D = 30), and CEC2005 F12 (D = 30).
Fig. 6. Average convergence profile of the median NPV value and its interquartile ranges of GP-LCB, GP-EI, IKAEA, ESAO, TS-DDEO, SA-MPSO, GL-SADE, ESA, and AutoSAEA on the oil reservoir production optimization problem.
1000, respectively. Moreover, each algorithm conducts five independent runs. The statistical results are provided in Table III. It can be seen that AutoSAEA can get the best average performance. Based on the Wilcoxon rank-sum test, AutoSAEA is significantly better than IKAEA, ESAO, and SA-MPSO and performs competitively with GP-LCB, GP-EI, ESA, TS-DDEO, and GL-SADE. Moreover, the running time of AutoSAEA is also the lowest, which shows the better computational efficiency of AutoSAEA compared with its competitors. Besides, the average convergence performance of each algorithm on this problem is plotted in Fig. 6, from which we can find
that AutoSAEA also has some advantages in solving this oil reservoir production optimization problem.
VI. CONCLUSION
The surrogate model and infill criterion are vital to the performance of SAEAs. To enhance the ability of SAEA to solve various EOPs, this article proposes a TL-MAB to cooperatively choose the surrogate model and infill criterion in an online manner. To achieve this, a TL-R mechanism is defined to measure the optimization utility of the surrogate model and infill criterion in a hierarchical and coupled manner. Additionally, a TL-UCB strategy is designed to choose them cooperatively and adaptively. With these adaptive selection components, an auto SAEA has been proposed, called AutoSAEA. The performance of AutoSAEA has been demonstrated by comparing it with several state-of-the-art SAEAs on two sets of benchmark problems (CEC2005 and CEC2015) and a real-world oil reservoir production optimization problem. Furthermore, the adaptive behavior of AutoSAEA has been numerically verified, and the sensitivity of its control parameters has been analyzed. In the future, we plan to extend the adaptive surrogate model and infill criterion selection method to expensive multiobjective optimization. Moreover, we will use the proposed AutoSAEA to tackle other real-world EOPs.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


XIE et al.: SAEA 1125
REFERENCES
[1] Y. Lian, A. Oyama, and M.-S. Liou, “Progress in design optimization using evolutionary algorithms for aerodynamic problems,” Progr. Aerosp. Sci., vol. 46, nos. 5–6, pp. 199–223, 2010. [2] T. W. Simpson, A. J. Booker, D. Ghosh, A. A. Giunta, P. N. Koch, and R.-J. Yang, “Approximation methods in multidisciplinary analysis and optimization: A panel discussion,” Struct. Multidiscipl. Optim., vol. 27, no. 5, pp. 302–313, 2004. [3] Q. Chen, G. Li, Q. Zhang, Q. Tang, and G. Zhang, “Optimal design of passive control of space tethered-net capture system,” IEEE Access, vol. 7, pp. 131383–131394, 2019. [4] Z. Wang, Y.-S. Ong, J. Sun, A. Gupta, and Q. Zhang, “A generator for multiobjective test problems with difficult-to-approximate Pareto front boundaries,” IEEE Trans. Evol. Comput., vol. 23, no. 4, pp. 556–571, Aug. 2019. [5] W. Gao, Z. Wei, M. Gong, and G. G. Yen, “Solving expensive multimodal optimization problem by a decomposition differential evolution algorithm,” IEEE Trans. Cybern., vol. 53, no. 4, pp. 2236–2246, Apr. 2023. [6] G. Li and Q. Zhang, “Multiple penalties and multiple local surrogates for expensive constrained optimization,” IEEE Trans. Evol. Comput., vol. 25, no. 4, pp. 769–778, Aug. 2021. [7] G. Li, L. Xie, Z. Wang, H. Wang, and M. Gong, “Evolutionary algorithm with individual-distribution search strategy and regression-classification surrogates for expensive optimization,” Inf. Sci., vol. 634, pp. 423–442, Jul. 2023. [8] H. Wang, H. Xu, and Z. Zhang, “High-dimensional multi-objective Bayesian optimization with block coordinate updates: Case studies in intelligent transportation system,” IEEE Trans. Intell. Transp. Syst., early access, Feb. 7, 2023, doi: 10.1109/TITS.2023.3241069. [9] T. Goel, R. T. Hafkta, and W. Shyy, “Comparing error estimation measures for polynomial and kriging approximation of noise-free functions,” Struct. Multidiscip. Optim., vol. 38, no. 5, pp. 429–442, 2009. [10] L. M. Zouhal and T. Denoeux, “An evidence-theoretic k-NN rule with parameter optimization,” IEEE Trans. Syst., Man, Cybern. C, Appl. Rev., vol. 28, no. 2, pp. 263–271, Mar. 1998. [11] S. M. Clarke, J. H. Griebsch, and T. W. Simpson, “Analysis of support vector regression for approximation of complex engineering analyses,” J. Mech. Design, vol. 127, no. 6, pp. 1077–1087, 2005. [12] Y. Jin, M. Olhofer, and B. Sendhoff, “A framework for evolutionary optimization with approximate fitness functions,” IEEE Trans. Evol. Comput., vol. 6, no. 5, pp. 481–494, Oct. 2002. [13] B. Liu, Q. Zhang, and G. G. E. Gielen, “A Gaussian process surrogate model assisted evolutionary algorithm for medium scale expensive optimization problems,” IEEE Trans. Evol. Comput., vol. 18, no. 2, pp. 180–192, Apr. 2014. [14] J. Tian, Y. Tan, J. Zeng, C. Sun, and Y. Jin, “Multiobjective infill criterion driven Gaussian process-assisted particle swarm optimization of highdimensional expensive problems,” IEEE Trans. Evol. Comput., vol. 23, no. 3, pp. 459–472, Jul. 2019. [15] F. Li, X. Cai, L. Gao, and W. Shen, “A surrogate-assisted multiswarm optimization algorithm for high-dimensional computationally expensive problems,” IEEE Trans. Cybern., vol. 51, no. 3, pp. 1390–1402, Mar. 2021. [16] Y. Liu, J. Liu, and Y. Jin, “Surrogate-assisted multipopulation particle swarm optimizer for high-dimensional expensive optimization,” IEEE Trans. Syst., Man, Cybern., Syst., vol. 52, no. 7, pp. 4671–4684, Jul. 2022. [17] J. Liu, Y. Wang, G. Sun, and T. Pang, “Multisurrogate-assisted ant colony optimization for expensive optimization problems with continuous and categorical variables,” IEEE Trans. Cybern., vol. 52, no. 11, pp. 11348–11361, Nov. 2022. [18] W. Wang, H.-L. Liu, and K. C. Tan, “A surrogate-assisted differential evolution algorithm for high-dimensional expensive optimization problems,” IEEE Trans. Cybern., vol. 53, no. 4, pp. 2685–2697, Apr. 2023. [19] H. Wang, Y. Jin, and J. Doherty, “Committee-based active learning for surrogate-assisted particle swarm optimization of expensive problems,” IEEE Trans. Cybern., vol. 47, no. 9, pp. 2664–2677, Sep. 2017. [20] T. Sonoda and M. Nakata, “Multiple classifiers-assisted evolutionary algorithm based on decomposition for high-dimensional multi-objective problems,” IEEE Trans. Evol. Comput., vol. 26, no. 6, pp. 1581–1595, Dec. 2022. [21] X. Wu, Q. Lin, J. Li, K. C. Tan, and V. C. Leung, “An ensemble surrogate-based coevolutionary algorithm for solving large-scale expensive optimization problems,” IEEE Trans. Cybern., early access, Sep. 16, 2022, doi: 10.1109/TCYB.2022.3200517.
[22] M. W. Hoffman, E. Brochu, and N. De Freitas, “Portfolio allocation for Bayesian optimization,” in Proc. UAI, 2011, pp. 327–336. [23] Z. Song, H. Wang, C. He, and Y. Jin, “A Kriging-assisted twoarchive evolutionary algorithm for expensive many-objective optimization,” IEEE Trans. Evol. Comput., vol. 25, no. 6, pp. 1013–1027, Dec. 2021. [24] Z. Liu, H. Wang, and Y. Jin, “Performance indicator-based adaptive model selection for offline data-driven multiobjective evolutionary optimization,” IEEE Trans. Cybern., early access, May 13, 2022, doi: 10.1109/TCYB.2022.3170344. [25] H. Zhen, W. Gong, and L. Wang, “Evolutionary sampling agent for expensive problems,” IEEE Trans. Evol. Comput., vol. 27, no. 3, pp. 716–727, Jun. 2023. [26] Q. Zhang, W. Liu, E. Tsang, and B. Virginas, “Expensive multiobjective optimization by MOEA/D with Gaussian process model,” IEEE Trans. Evol. Comput., vol. 14, no. 3, pp. 456–474, Jun. 2010. [27] J. E. Dennis and V. Torczon, “Managing approximation models in optimization,” Multidiscip. Design Optim. State-Art, vol. 5, pp. 330–347, Nov. 1998. [28] G. Li, Q. Zhang, Q. Lin, and W. Gao, “A three-level radial basis function method for expensive optimization,” IEEE Trans. Cybern., vol. 52, no. 7, pp. 5720–5731, Jul. 2022. [29] J. Zhang, A. Zhou, and G. Zhang, “A multiobjective evolutionary algorithm based on decomposition and preselection,” in Bio-Inspired Computing—Theories and Applications. Heidelberg, Germany: Springer, 2015, pp. 631–642. [30] M. Yu, X. Li, and J. Liang, “A dynamic surrogate-assisted evolutionary algorithm framework for expensive structural optimization,” Struct. Multidiscip. Optim., vol. 61, no. 2, pp. 711–729, 2020. [31] H. Zhen, W. Gong, L. Wang, F. Ming, and Z. Liao, “Two-stage datadriven evolutionary optimization for high-dimensional expensive problems,” IEEE Trans. Cybern., vol. 53, no. 4, pp. 2368–2379, Apr. 2023. [32] X. Wang, G. G. Wang, B. Song, P. Wang, and Y. Wang, “A novel evolutionary sampling assisted optimization method for high-dimensional expensive problems,” IEEE Trans. Evol. Comput., vol. 23, no. 5, pp. 815–827, Oct. 2019. [33] X. Cai, L. Gao, and X. Li, “Efficient generalized surrogate-assisted evolutionary algorithm for high-dimensional expensive problems,” IEEE Trans. Evol. Comput., vol. 24, no. 2, pp. 365–379, Apr. 2020. [34] Z. Wang et al., “Multiobjective optimization-aided decision-making system for large-scale manufacturing planning,” IEEE Trans. Cybern., vol. 52, no. 8, pp. 8326–8339, Aug. 2022. [35] D. Guo, Y. Jin, J. Ding, and T. Chai, “Heterogeneous ensemble-based infill criterion for evolutionary multiobjective optimization of expensive problems,” IEEE Trans. Cybern., vol. 49, no. 3, pp. 1012–1025, Mar. 2019. [36] J.-Y. Li, Z.-H. Zhan, H. Wang, and J. Zhang, “Data-driven evolutionary algorithm with perturbation-based ensemble surrogates,” IEEE Trans. Cybern., vol. 51, no. 8, pp. 3925–3937, Aug. 2021. [37] L. Kocsis and C. Szepesvári, “Bandit based Monte-Carlo planning,” in Proc. Eur. Conf. Mach. Learn., 2006, pp. 282–293.
[38] S. Pandey, D. Chakrabarti, and D. Agarwal, “Multi-armed bandit problems with dependent arms,” in Proc. 24th Int. Conf. Mach. Learn., 2007, pp. 721–728. [39] E. Carlsson, D. Dubhashi, and F. D. Johansson, “Thompson sampling for bandits with clustered arms,” 2021, arXiv:2109.01656. [40] D. R. Jones, M. Schonlau, and W. J. Welch, “Efficient global optimization of expensive black-box functions,” J. Global Optim., vol. 13, no. 4, p. 455, 1998. [41] P. I. Frazier, “A tutorial on Bayesian optimization,” 2018, arXiv:1807.02811.
[42] D. Zhan and H. Xing, “A fast Kriging-assisted evolutionary algorithm based on incremental learning,” IEEE Trans. Evol. Comput., vol. 25, no. 5, pp. 941–955, Oct. 2021. [43] D. D. Cox and S. John, “A statistical method for global optimization,” in Proc. IEEE Int. Conf. Syst., Man, Cybern., 1992, pp. 1241–1246.
[44] J. Zhang, A. Zhou, and G. Zhang, “A classification and Pareto domination based multiobjective evolutionary algorithm,” in Proc. IEEE Congr. Evol. Comput. (CEC), 2015, pp. 2883–2890. [45] C. Sun, Y. Jin, R. Cheng, J. Ding, and J. Zeng, “Surrogate-assisted cooperative swarm optimization of high-dimensional expensive problems,” IEEE Trans. Evol. Comput., vol. 21, no. 4, pp. 644–660, Aug. 2017. [46] Y. Haibo, T. Ying, Z. Jianchao, S. Chaoli, and J. Yaochu, “Surrogate-assisted hierarchical particle swarm optimization,” Inf. Sci., vols. 454–455, pp. 59–72, Jul. 2018.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.


1126 IEEE TRANSACTIONS ON EVOLUTIONARY COMPUTATION, VOL. 28, NO. 4, AUGUST 2024
[47] F.-F. Wei et al., “A classifier-assisted level-based learning swarm optimizer for expensive optimization,” IEEE Trans. Evol. Comput., vol. 25, no. 2, pp. 219–233, Apr. 2020. [48] Q. Liu, R. Cheng, Y. Jin, M. Heiderich, and T. Rodemann, “Reference vector-assisted adaptive model management for surrogate-assisted manyobjective optimization,” IEEE Trans. Syst., Man, Cybern., Syst., vol. 52, no. 12, pp. 7760–7773, Dec. 2022. [49] G. Li, Q. Zhang, J. Sun, and Z. Han, “Radial basis function assisted optimization method with batch infill sampling criterion for expensive optimization,” in Proc. IEEE Congr. Evol. Comput. (CEC), 2019, pp. 1664–1671. [50] A. I. Khuri and S. Mukhopadhyay, “Response surface methodology,” Wiley Interdiscipl. Rev. Comput. Stat., vol. 2, no. 2, pp. 128–149, 2010. [51] W. Gao, G. Li, Q. Zhang, Y. Luo, and Z. Wang, “Solving nonlinear equation systems by a two-phase evolutionary algorithm,” IEEE Trans. Syst., Man, Cybern., Syst., vol. 51, no. 9, pp. 5652–5663, Sep. 2021. [52] L. Pan, C. He, Y. Tian, H. Wang, X. Zhang, and Y. Jin, “A classificationbased surrogate-assisted evolutionary algorithm for expensive manyobjective optimization,” IEEE Trans. Evol. Comput., vol. 23, no. 1, pp. 74–88, Feb. 2019. [53] T. Zhao, M. Li, and M. Poloczek, “Fast reconfigurable antenna state selection with hierarchical Thompson sampling,” in Proc. IEEE Int. Conf. Commun. (ICC), 2019, pp. 1–6.
[54] R. Singh, F. Liu, Y. Sun, and N. Shroff, “Multi-armed bandits with dependent arms,” 2020, arXiv:2010.09478. [55] J. Hong, B. Kveton, M. Zaheer, and M. Ghavamzadeh, “Hierarchical Bayesian bandits,” in Proc. Int. Conf. Artif. Intell. Stat., 2022, pp. 7724–7741. [56] P. N. Suganthan et al., “Problem definitions and evaluation criteria for the CEC 2005 special session on real-parameter optimization,” Nanyang Technol. Univ., Singapore, IIT, Kanpur, India, KanGAL Rep. #2005005, 2005. [57] J. Liang, B. Qu, P. Suganthan, and Q. Chen, “Problem definitions and evaluation criteria for the CEC 2015 competition on learning-based realparameter single objective optimization,” Comput. Intell. Lab., Nanyang Technol. Univ., Singapore, Rep. 201411A, 2014. [58] G. Li, Z. Wang, and M. Gong, “Expensive optimization via surrogateassisted and model-free evolutionary optimization,” IEEE Trans. Syst., Man, Cybern., Syst., vol. 53, no. 5, pp. 2758–2769, May 2023. [59] Z. Wang, Y.-S. Ong, and H. Ishibuchi, “On scalable multiobjective test problems with hardly dominated boundaries,” IEEE Trans. Evol. Comput., vol. 23, no. 2, pp. 217–231, Apr. 2019. [60] G. Chen, X. Luo, J. J. Jiao, and X. Xue, “Data-driven evolutionary algorithm for oil reservoir well-placement and control optimization,” Fuel, vol. 326, Oct. 2022, Art. no. 125125. [61] J.-D. Jansen, R.-M. Fonseca, S. Kahrobaei, M. Siraj, G. Van Essen, and P. Van den Hof, “The egg model—A geological ensemble for reservoir simulation,” Geosci. Data J., vol. 1, no. 2, pp. 192–195, 2014.
[62] K.-A. Lie, An Introduction to Reservoir Simulation Using MATLAB/GNU Octave: User Guide for the MATLAB Reservoir Simulation Toolbox (MRST). Cambridge, U.K.: Cambridge Univ. Press, 2019.
Lindong Xie received the B.S. degree in mechanical and electronic engineering from Henan University of Science and Technology, Luoyang, China, in 2021. He is currently pursuing the M.S. degree with the School of System Design and Intelligent Manufacturing, Southern University of Science and Technology, Shenzhen, China. His current research interests include data-driven evolutionary algorithms, and machine learning and their applications.
Genghui Li received the M.Sc. degree in computer science and technology from Shenzhen University, Shenzhen, China, in 2016, and the Ph.D. degree in computer science from the City University of Hong Kong, Hong Kong, China, in 2021. He is currently a Postdoctoral Fellow with the Southern University of Science and Technology, Shenzhen. His research interests include evolutionary computation, computational intelligence, and machine learning.
Zhenkun Wang (Member, IEEE) received the Ph.D. degree in circuits and systems from Xidian University, Xi’an, China, in 2016. From 2017 to 2020, he was a Postdoctoral Research Fellow with the School of Computer Science and Engineering, Nanyang Technological University, Singapore, and with the Department of Computer Science, City University of Hong Kong, Hong Kong. He is currently an Assistant Professor with the School of System Design and Intelligent Manufacturing and the Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China. His research interests include evolutionary computation, optimization, machine learning, and their applications. Dr. Wang is an Associate Editor of the Swarm and Evolutionary Computation.
Laizhong Cui (Senior Member, IEEE) received the B.S. degree from Jilin University, Changchun, China, in 2007, and the Ph.D. degree in computer science and technology from Tsinghua University, Beijing, China, in 2012. He is currently a Professor with the College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, China. He led more than ten scientific research projects, including National Key Research and Development Plan of China, National Natural Science Foundation of China, Guangdong Natural Science Foundation of China, and Shenzhen Basic Research Plan. He has published more than 100 papers, including IEEE JOURNAL ON SELECTED AREAS IN COMMUNICATIONS, IEEE TRANSACTIONS ON COMPUTERS, IEEE TRANSACTIONS ON PARALLEL AND DISTRIBUTED SYSTEMS, IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, IEEE TRANSACTIONS ON MULTIMEDIA, IEEE INTERNET OF THINGS JOURNAL, IEEE TRANSACTIONS ON INDUSTRIAL INFORMATICS, IEEE TRANSACTIONS ON VEHICULAR TECHNOLOGY, IEEE TRANSACTIONS ON NETWORK AND SERVICE MANAGEMENT, ACM Transactions on Internet Technology, IEEE NETWORK, IEEE INFOCOM, ACM MM, IEEE ICNP, and IEEE ICDCS. His research interests include future Internet architecture and protocols, edge computing, multimedia systems and applications, Blockchain, Internet of Things, cloud computing, and federated learning. Prof. Cui serves as an Associate Editor or a member of Editorial Board for several international journals, including IEEE INTERNET OF THINGS JOURNAL, IEEE TRANSACTIONS ON CLOUD COMPUTING, IEEE TRANSACTIONS ON NETWORK AND SERVICE MANAGEMENT, and International Journal of Machine Learning and Cybernetics. He is a Distinguished Member of the CCF.
Maoguo Gong (Senior Member, IEEE) received the B.Eng. degree (Hons.) in electronic engineering and Ph.D. degree in electronic science and technology from Xidian University, Xi’an, China, in 2003 and 2009, respectively. Since 2006, he has been a Teacher with Xidian University. He was promoted to an Associate Professor and a Full Professor in 2008 and 2010, respectively, both with exceptive admission. He is leading or has completed over 20 projects as the Principle Investigator, funded by the National Natural Science Foundation of China and the National Key Research and Development Program of China. He has published over 100 papers in journals and conferences, and holds over 20 granted patents as the first inventor. His research interests are broadly in the area of computational intelligence, with applications to optimization, learning, data mining, and image understanding. Dr. Gong is the Director of the Chinese Association for Artificial Intelligence-Youth Branch, the Senior Member of Chinese Computer Federation, and an Associate Editor or an Editorial Board Member for over five journals, including the IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS and the IEEE TRANSACTIONS ON EMERGING TOPICS IN COMPUTATIONAL INTELLIGENCE.
Authorized licensed use limited to: Taiyuan University of Science & Technology. Downloaded on June 03,2025 at 14:26:11 UTC from IEEE Xplore. Restrictions apply.