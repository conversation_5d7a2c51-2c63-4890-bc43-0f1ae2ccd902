"""
联邦AutoSAEA客户端实现
专注于本地任务优化和知识利用
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Callable
from autosaea import AutoSAEA, TwoLevelMAB
import copy

class TaskSpecificAutoSAEA(AutoSAEA):
    """任务特定的AutoSAEA"""
    
    def __init__(self, task_id: str, objective_func: Callable, bounds: np.ndarray,
                 population_size: int = 50, max_fes: int = 500, alpha: float = 2.0):
        super().__init__(objective_func, bounds, population_size, max_fes, alpha)
        
        self.task_id = task_id
        self.task_type = "unknown"
        
        # 任务特定的学习参数
        self.task_adaptation_rate = 0.1
        self.knowledge_utilization_weight = 0.3
        self.local_experience_weight = 0.7
        
        # 知识管理
        self.external_knowledge = {}
        self.knowledge_effectiveness = {}
        self.similar_tasks_knowledge = {}
        
        # 性能跟踪
        self.model_local_performance = {model: [] for model in self.models.keys()}
        self.criterion_local_performance = {criterion: [] for criterion in self.criteria.keys()}
        self.adaptation_history = []
        
    def initialize_from_knowledge(self, knowledge: Dict):
        """从外部知识初始化"""
        self.external_knowledge = knowledge
        
        # 1. 初始化MAB策略
        if 'model_preferences' in knowledge:
            for model, preference in knowledge['model_preferences'].items():
                if model in self.mab.high_values:
                    # 结合外部知识和本地初始值
                    self.mab.high_values[model] = (
                        self.knowledge_utilization_weight * preference +
                        self.local_experience_weight * self.mab.high_values[model]
                    )
        
        if 'criterion_preferences' in knowledge:
            for criterion, preference in knowledge['criterion_preferences'].items():
                if criterion in self.mab.low_values:
                    self.mab.low_values[criterion] = (
                        self.knowledge_utilization_weight * preference +
                        self.local_experience_weight * self.mab.low_values[criterion]
                    )
        
        # 2. 初始化种群（如果有相关解决方案）
        if 'initialization_solutions' in knowledge and knowledge['initialization_solutions']:
            self._initialize_population_from_solutions(knowledge['initialization_solutions'])
        
        # 3. 设置任务特定参数
        if 'landscape_hints' in knowledge:
            self._adapt_parameters_from_landscape(knowledge['landscape_hints'])
    
    def _initialize_population_from_solutions(self, solutions: List[np.ndarray]):
        """从已知解决方案初始化种群"""
        if not solutions:
            return
        
        # 选择最多population_size//2个解作为初始种群的一部分
        n_init = min(len(solutions), self.population_size // 2)
        selected_solutions = solutions[:n_init]
        
        # 在选定解周围生成扰动解
        perturbed_solutions = []
        for sol in selected_solutions:
            # 确保解在边界内
            sol = np.clip(sol, self.bounds[:, 0], self.bounds[:, 1])
            
            # 生成扰动版本
            noise_scale = 0.1 * (self.bounds[:, 1] - self.bounds[:, 0])
            for _ in range(2):  # 每个解生成2个扰动版本
                perturbed = sol + np.random.normal(0, noise_scale)
                perturbed = np.clip(perturbed, self.bounds[:, 0], self.bounds[:, 1])
                perturbed_solutions.append(perturbed)
        
        # 评估这些解并添加到数据库
        for sol in selected_solutions + perturbed_solutions:
            if self.fes_count < self.max_fes:
                self._evaluate_solution(sol)
    
    def _adapt_parameters_from_landscape(self, landscape_hints: Dict):
        """根据景观提示调整参数"""
        if not landscape_hints:
            return
        
        # 根据问题复杂度调整探索参数
        if 'modality_estimate' in landscape_hints:
            modality = landscape_hints['modality_estimate']
            if modality > 5:  # 高度多模态
                self.alpha *= 1.5  # 增加探索
                self.de_operator.F *= 1.2  # 增加变异强度
            elif modality < 2:  # 单模态
                self.alpha *= 0.8  # 减少探索
                self.de_operator.F *= 0.8  # 减少变异强度
        
        # 根据收敛特性调整
        if 'convergence_rate' in landscape_hints:
            conv_rate = landscape_hints['convergence_rate']
            if conv_rate < 0.1:  # 收敛困难
                self.de_operator.CR *= 1.1  # 增加交叉概率
            elif conv_rate > 0.5:  # 收敛快速
                self.de_operator.CR *= 0.9  # 减少交叉概率
    
    def adaptive_optimize(self, server_knowledge_callback: Optional[Callable] = None,
                         knowledge_update_frequency: int = 50) -> Tuple[np.ndarray, float]:
        """自适应优化主循环"""
        
        print(f"开始任务 {self.task_id} 的自适应优化...")
        
        # 初始化种群
        if len(self.database) == 0:
            initial_population = self._latin_hypercube_sampling(self.population_size)
            for x in initial_population:
                self._evaluate_solution(x)
        
        # 主优化循环
        t = 1
        while self.fes_count < self.max_fes:
            # 获取当前种群
            current_pop, current_fitness = self._get_current_population()
            current_population = np.column_stack([current_pop, current_fitness])
            f_min = np.min(current_fitness)
            
            # 自适应模型和准则选择
            model_name, criterion_name = self._adaptive_model_selection(t)
            
            # 生成新解
            try:
                new_x = self._generate_new_solution(model_name, criterion_name,
                                                  current_population, f_min)
                new_fitness = self._evaluate_solution(new_x)
                
                # 更新MAB奖励
                self.mab.update_rewards(model_name, criterion_name, current_population,
                                      new_x, new_fitness)
                
                # 记录本地性能
                self._record_local_performance(model_name, criterion_name, new_fitness, f_min)
                
                # 更新历史
                self.history['best_fitness'].append(np.min([sol[-1] for sol in self.database]))
                self.history['selected_models'].append(model_name)
                self.history['selected_criteria'].append(criterion_name)
                
                # 定期更新知识
                if (server_knowledge_callback and 
                    t % knowledge_update_frequency == 0):
                    self._update_with_server_knowledge(server_knowledge_callback)
                
                if t % 25 == 0:
                    print(f"任务 {self.task_id} - 迭代 {t}, FEs: {self.fes_count}, "
                          f"最佳适应度: {self.history['best_fitness'][-1]:.6f}")
                    print(f"选择: {model_name} + {criterion_name}")
                
            except Exception as e:
                print(f"任务 {self.task_id} 迭代 {t} 出错: {e}")
                # 生成随机解作为备选
                new_x = np.random.uniform(self.bounds[:, 0], self.bounds[:, 1])
                self._evaluate_solution(new_x)
            
            t += 1
        
        # 返回最佳解
        best_data = min(self.database, key=lambda x: x[-1])
        best_x = best_data[:-1]
        best_fitness = best_data[-1]
        
        print(f"任务 {self.task_id} 优化完成!")
        print(f"最佳适应度: {best_fitness:.6f}")
        print(f"总FEs: {self.fes_count}")
        
        return best_x, best_fitness
    
    def _adaptive_model_selection(self, t: int) -> Tuple[str, str]:
        """自适应模型选择"""
        # 结合本地经验和外部知识
        if t <= len(self.mab.combinatorial_arms):
            return self.mab.select_arms(t)
        
        # 计算增强的UCB值
        high_ucb_values = {}
        for arm in self.mab.high_arms:
            if self.mab.high_counts[arm] == 0:
                high_ucb_values[arm] = float('inf')
            else:
                # 基础UCB
                base_ucb = self.mab.high_values[arm] + np.sqrt(
                    self.alpha * np.log(t) / self.mab.high_counts[arm]
                )
                
                # 外部知识增强
                knowledge_bonus = 0.0
                if ('model_preferences' in self.external_knowledge and 
                    arm in self.external_knowledge['model_preferences']):
                    knowledge_bonus = 0.1 * self.external_knowledge['model_preferences'][arm]
                
                # 本地性能增强
                local_bonus = 0.0
                if self.model_local_performance[arm]:
                    recent_performance = np.mean(self.model_local_performance[arm][-10:])
                    local_bonus = 0.1 * recent_performance
                
                high_ucb_values[arm] = base_ucb + knowledge_bonus + local_bonus
        
        selected_high = max(high_ucb_values, key=high_ucb_values.get)
        
        # 选择低层臂
        associated_low_arms = self.mab.associations[selected_high]
        low_ucb_values = {}
        
        for arm in associated_low_arms:
            if self.mab.low_counts[arm] == 0:
                low_ucb_values[arm] = float('inf')
            else:
                base_ucb = self.mab.low_values[arm] + np.sqrt(
                    self.alpha * np.log(t) / self.mab.low_counts[arm]
                )
                
                # 外部知识增强
                knowledge_bonus = 0.0
                if ('criterion_preferences' in self.external_knowledge and 
                    arm in self.external_knowledge['criterion_preferences']):
                    knowledge_bonus = 0.1 * self.external_knowledge['criterion_preferences'][arm]
                
                low_ucb_values[arm] = base_ucb + knowledge_bonus
        
        selected_low = max(low_ucb_values, key=low_ucb_values.get)
        
        return selected_high, selected_low
    
    def _record_local_performance(self, model_name: str, criterion_name: str, 
                                new_fitness: float, current_best: float):
        """记录本地性能"""
        # 计算改进度
        improvement = max(0, current_best - new_fitness) / (abs(current_best) + 1e-8)
        
        # 记录模型性能
        self.model_local_performance[model_name].append(improvement)
        if len(self.model_local_performance[model_name]) > 50:
            self.model_local_performance[model_name].pop(0)  # 保持固定长度
        
        # 记录准则性能
        self.criterion_local_performance[criterion_name].append(improvement)
        if len(self.criterion_local_performance[criterion_name]) > 50:
            self.criterion_local_performance[criterion_name].pop(0)
    
    def _update_with_server_knowledge(self, server_callback: Callable):
        """更新服务器知识"""
        try:
            # 准备本地知识报告
            local_report = self.prepare_knowledge_report()
            
            # 获取服务器更新的知识
            updated_knowledge = server_callback(self.task_id, local_report)
            
            if updated_knowledge:
                # 更新外部知识
                self.external_knowledge.update(updated_knowledge)
                
                # 自适应调整权重
                self._adapt_knowledge_weights()
                
        except Exception as e:
            print(f"任务 {self.task_id} 知识更新失败: {e}")
    
    def _adapt_knowledge_weights(self):
        """自适应调整知识权重"""
        # 根据本地性能调整外部知识的权重
        if len(self.history['best_fitness']) > 20:
            recent_improvement = (self.history['best_fitness'][-20] - 
                                self.history['best_fitness'][-1])
            
            if recent_improvement > 0:
                # 性能改善，增加外部知识权重
                self.knowledge_utilization_weight = min(0.5, 
                    self.knowledge_utilization_weight * 1.1)
            else:
                # 性能停滞，减少外部知识权重
                self.knowledge_utilization_weight = max(0.1, 
                    self.knowledge_utilization_weight * 0.9)
            
            self.local_experience_weight = 1.0 - self.knowledge_utilization_weight
    
    def prepare_knowledge_report(self) -> Dict:
        """准备知识报告"""
        # 选择最优解
        best_solutions = sorted(self.database, key=lambda x: x[-1])[:10]
        
        report = {
            'task_id': self.task_id,
            'best_solutions': [sol[:-1] for sol in best_solutions],
            'fitness_values': [sol[-1] for sol in best_solutions],
            'model_performance': self.mab.high_values.copy(),
            'criterion_performance': self.mab.low_values.copy(),
            'local_model_performance': {
                model: np.mean(perfs[-10:]) if perfs else 0.0 
                for model, perfs in self.model_local_performance.items()
            },
            'convergence_status': self._assess_convergence_status(),
            'total_fes': self.fes_count,
            'current_best_fitness': min(sol[-1] for sol in self.database) if self.database else float('inf')
        }
        
        return report
    
    def _assess_convergence_status(self) -> Dict:
        """评估收敛状态"""
        if len(self.history['best_fitness']) < 10:
            return {'status': 'early', 'improvement_rate': 0.0}
        
        recent_fitness = self.history['best_fitness'][-10:]
        improvement = recent_fitness[0] - recent_fitness[-1]
        improvement_rate = improvement / (abs(recent_fitness[0]) + 1e-8)
        
        if improvement_rate < 0.001:
            status = 'converged'
        elif improvement_rate < 0.01:
            status = 'slow_progress'
        else:
            status = 'active'
        
        return {
            'status': status,
            'improvement_rate': improvement_rate,
            'stagnation_count': self._count_stagnation_iterations()
        }
    
    def _count_stagnation_iterations(self) -> int:
        """计算停滞迭代数"""
        if len(self.history['best_fitness']) < 5:
            return 0
        
        current_best = self.history['best_fitness'][-1]
        stagnation_count = 0
        
        for i in range(len(self.history['best_fitness']) - 1, -1, -1):
            if abs(self.history['best_fitness'][i] - current_best) < 1e-8:
                stagnation_count += 1
            else:
                break
        
        return stagnation_count
    
    def get_task_statistics(self) -> Dict:
        """获取任务统计信息"""
        base_stats = self.get_statistics()
        
        # 添加任务特定统计
        task_stats = {
            'task_id': self.task_id,
            'knowledge_utilization_weight': self.knowledge_utilization_weight,
            'local_model_performance': {
                model: {
                    'mean': np.mean(perfs) if perfs else 0.0,
                    'std': np.std(perfs) if perfs else 0.0,
                    'count': len(perfs)
                }
                for model, perfs in self.model_local_performance.items()
            },
            'convergence_assessment': self._assess_convergence_status(),
            'adaptation_effectiveness': self._compute_adaptation_effectiveness()
        }
        
        base_stats.update(task_stats)
        return base_stats
    
    def _compute_adaptation_effectiveness(self) -> float:
        """计算适应性效果"""
        if len(self.history['best_fitness']) < 20:
            return 0.0
        
        # 比较前半段和后半段的改进率
        mid_point = len(self.history['best_fitness']) // 2
        early_improvement = (self.history['best_fitness'][0] - 
                           self.history['best_fitness'][mid_point])
        late_improvement = (self.history['best_fitness'][mid_point] - 
                          self.history['best_fitness'][-1])
        
        if early_improvement <= 0:
            return 0.0
        
        effectiveness = late_improvement / early_improvement
        return max(0.0, min(2.0, effectiveness))  # 限制在[0, 2]范围内
