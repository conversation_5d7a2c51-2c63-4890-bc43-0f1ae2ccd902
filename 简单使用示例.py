"""
联邦多任务AutoSAEA简单使用示例
演示如何快速上手使用联邦多任务优化
"""

import numpy as np
from federated_multitask_coordinator import FederatedMultiTaskCoordinator

def main():
    """简单使用示例"""
    print("联邦多任务AutoSAEA简单使用示例")
    print("="*40)
    
    # 步骤1: 定义优化问题
    def problem_1(x):
        """问题1: 寻找点(1,1,1)"""
        return np.sum((x - 1)**2)
    
    def problem_2(x):
        """问题2: 寻找点(2,2,2)"""
        return np.sum((x - 2)**2)
    
    def problem_3(x):
        """问题3: 寻找点(0,0,0)"""
        return np.sum(x**2)
    
    # 步骤2: 设置搜索空间
    dimension = 3
    bounds = np.array([[-5, 5]] * dimension)
    
    # 步骤3: 创建联邦优化协调器
    coordinator = FederatedMultiTaskCoordinator(
        global_bounds=bounds,
        max_parallel_tasks=2  # 最多并行2个任务
    )
    
    # 步骤4: 注册优化任务
    tasks = {
        'find_point_1': (problem_1, 'quadratic'),
        'find_point_2': (problem_2, 'quadratic'), 
        'find_origin': (problem_3, 'quadratic')
    }
    
    print("注册优化任务...")
    for task_id, (objective, task_type) in tasks.items():
        success = coordinator.register_task(
            task_id=task_id,
            objective_func=objective,
            task_type=task_type,
            population_size=20,  # 种群大小
            max_fes=150         # 最大函数评估次数
        )
        print(f"  {task_id}: {'成功' if success else '失败'}")
    
    # 步骤5: 执行联邦优化
    print(f"\n开始联邦多任务优化...")
    results = coordinator.optimize_all_tasks(parallel=True)
    
    # 步骤6: 查看结果
    print(f"\n优化结果:")
    print("-" * 50)
    
    expected_solutions = {
        'find_point_1': [1, 1, 1],
        'find_point_2': [2, 2, 2],
        'find_origin': [0, 0, 0]
    }
    
    for task_id, result in results.items():
        if 'error' not in result:
            best_solution = result['best_solution']
            best_fitness = result['best_fitness']
            expected = expected_solutions[task_id]
            
            print(f"{task_id}:")
            print(f"  找到的解: [{best_solution[0]:.3f}, {best_solution[1]:.3f}, {best_solution[2]:.3f}]")
            print(f"  期望的解: {expected}")
            print(f"  适应度值: {best_fitness:.6f}")
            print(f"  函数评估次数: {result['statistics']['total_fes']}")
            
            # 计算误差
            error = np.linalg.norm(best_solution - np.array(expected))
            print(f"  与期望解的距离: {error:.6f}")
            print()
        else:
            print(f"{task_id}: 优化失败 - {result['error']}")
    
    # 步骤7: 分析任务间的知识共享效果
    print("任务相似性分析:")
    print("-" * 30)
    
    relationships = coordinator.analyze_task_relationships()
    if 'high_similarity_pairs' in relationships:
        for pair in relationships['high_similarity_pairs']:
            print(f"  {pair['task1']} ↔ {pair['task2']}: 相似度 {pair['similarity']:.3f}")
    
    if not relationships.get('high_similarity_pairs'):
        print("  未发现高相似性任务对")
    
    # 步骤8: 显示优化总结
    print(f"\n" + "="*50)
    coordinator.print_summary()
    
    # 步骤9: 保存结果（可选）
    coordinator.save_results('simple_example_results.json')
    print(f"\n详细结果已保存到 'simple_example_results.json'")

if __name__ == "__main__":
    main()
