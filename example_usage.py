"""
Simple example of using AutoSAEA algorithm
"""

import numpy as np
import matplotlib.pyplot as plt
from autosaea import AutoSAEA

def simple_quadratic(x):
    """Simple quadratic function: f(x) = sum((x_i - 1)^2)"""
    return np.sum((x - 1)**2)

def main():
    print("AutoSAEA Simple Example")
    print("=" * 40)
    
    # Define the problem
    dimension = 5
    bounds = np.array([[-5, 5]] * dimension)  # Each variable in [-5, 5]
    
    print(f"Problem: Minimize sum((x_i - 1)^2) for i=1..{dimension}")
    print(f"Bounds: {bounds[0]}")
    print(f"Global optimum: x = [1, 1, 1, 1, 1], f(x) = 0")
    
    # Create and run AutoSAEA
    optimizer = AutoSAEA(
        objective_func=simple_quadratic,
        bounds=bounds,
        population_size=20,
        max_fes=150,
        alpha=2.0
    )
    
    # Run optimization
    best_x, best_fitness = optimizer.optimize()
    
    # Display results
    print(f"\nOptimization Results:")
    print(f"Best solution found: {best_x}")
    print(f"Best fitness: {best_fitness:.8f}")
    print(f"Distance from global optimum: {np.linalg.norm(best_x - 1):.8f}")
    
    # Get detailed statistics
    stats = optimizer.get_statistics()
    
    print(f"\nAlgorithm Statistics:")
    print(f"Total function evaluations: {stats['total_fes']}")
    
    print(f"\nModel Selection Summary:")
    for model, count in stats['model_selection_counts'].items():
        percentage = (count / sum(stats['model_selection_counts'].values())) * 100
        print(f"  {model:8}: {count:3d} times ({percentage:5.1f}%)")
    
    print(f"\nFinal Model Values (higher = better):")
    for model, value in stats['final_model_values'].items():
        print(f"  {model:8}: {value:8.4f}")
    
    print(f"\nActive Criterion Selection Summary:")
    active_criteria = {k: v for k, v in stats['criterion_selection_counts'].items() if v > 0}
    for criterion, count in active_criteria.items():
        percentage = (count / sum(active_criteria.values())) * 100
        print(f"  {criterion:20}: {count:3d} times ({percentage:5.1f}%)")
    
    # Plot results
    plt.figure(figsize=(15, 5))
    
    # Convergence curve
    plt.subplot(1, 3, 1)
    plt.plot(optimizer.history['best_fitness'])
    plt.xlabel('Iteration')
    plt.ylabel('Best Fitness')
    plt.title('Convergence Curve')
    plt.yscale('log')
    plt.grid(True, alpha=0.3)
    
    # Model selection over time
    plt.subplot(1, 3, 2)
    models = optimizer.history['selected_models']
    model_colors = {'GP': 'red', 'RBF': 'blue', 'PRS': 'green', 'KNN': 'orange'}
    
    for i, model in enumerate(models):
        plt.scatter(i, model, c=model_colors[model], alpha=0.7, s=30)
    
    plt.xlabel('Iteration')
    plt.ylabel('Selected Model')
    plt.title('Model Selection Over Time')
    plt.grid(True, alpha=0.3)
    
    # Model values evolution
    plt.subplot(1, 3, 3)
    for model, values in optimizer.history['model_values'].items():
        if len(values) > 0:
            plt.plot(values, label=model, color=model_colors[model], linewidth=2)
    
    plt.xlabel('Iteration')
    plt.ylabel('Model Value')
    plt.title('Model Values Evolution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Show final model preferences
    plt.figure(figsize=(10, 4))
    
    plt.subplot(1, 2, 1)
    models = list(stats['model_selection_counts'].keys())
    counts = list(stats['model_selection_counts'].values())
    colors = [model_colors[model] for model in models]
    
    plt.bar(models, counts, color=colors, alpha=0.7)
    plt.xlabel('Model')
    plt.ylabel('Selection Count')
    plt.title('Model Selection Frequency')
    plt.xticks(rotation=45)
    
    plt.subplot(1, 2, 2)
    values = list(stats['final_model_values'].values())
    plt.bar(models, values, color=colors, alpha=0.7)
    plt.xlabel('Model')
    plt.ylabel('Final Value')
    plt.title('Final Model Values')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    main()
