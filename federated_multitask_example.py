"""
联邦多任务优化示例
演示AutoSAEA在多任务协同优化中的应用
"""

import numpy as np
import matplotlib.pyplot as plt
from federated_multitask_coordinator import FederatedMultiTaskCoordinator

# 定义多个相关的优化任务
class OptimizationTasks:
    """优化任务集合"""
    
    @staticmethod
    def shifted_sphere(shift_vector):
        """平移的球函数"""
        def objective(x):
            return np.sum((x - shift_vector)**2)
        return objective
    
    @staticmethod
    def rotated_ellipsoid(rotation_angle, axis_ratios):
        """旋转椭球函数"""
        def objective(x):
            # 简化的2D旋转
            if len(x) >= 2:
                cos_a, sin_a = np.cos(rotation_angle), np.sin(rotation_angle)
                x_rot = x.copy()
                x_rot[0] = cos_a * x[0] - sin_a * x[1]
                x_rot[1] = sin_a * x[0] + cos_a * x[1]
                return np.sum(axis_ratios * x_rot**2)
            else:
                return np.sum(axis_ratios[:len(x)] * x**2)
        return objective
    
    @staticmethod
    def shifted_rosenbrock(shift_vector, scale=100):
        """平移的Rosenbrock函数"""
        def objective(x):
            x_shifted = x - shift_vector
            return np.sum(scale * (x_shifted[1:] - x_shifted[:-1]**2)**2 + (1 - x_shifted[:-1])**2)
        return objective
    
    @staticmethod
    def multimodal_function(centers, widths):
        """多模态函数"""
        def objective(x):
            values = []
            for center, width in zip(centers, widths):
                dist = np.linalg.norm(x - center)
                values.append(np.exp(-dist**2 / (2 * width**2)))
            return -np.max(values) + 1  # 转换为最小化问题
        return objective
    
    @staticmethod
    def constrained_quadratic(center, penalty_weight=1000):
        """带约束的二次函数"""
        def objective(x):
            # 基础二次函数
            base_value = np.sum((x - center)**2)
            
            # 添加约束惩罚（例如：x[0] + x[1] <= 1）
            if len(x) >= 2:
                constraint_violation = max(0, x[0] + x[1] - 1)
                penalty = penalty_weight * constraint_violation**2
                return base_value + penalty
            
            return base_value
        return objective

def create_related_tasks(dimension=5):
    """创建相关的优化任务"""
    bounds = np.array([[-5, 5]] * dimension)
    tasks = {}
    
    # 任务组1: 相似的球函数（不同平移）
    tasks['sphere_1'] = {
        'objective': OptimizationTasks.shifted_sphere(np.array([1.0] * dimension)),
        'type': 'sphere',
        'description': '平移球函数 - 中心在 [1,1,1,1,1]'
    }
    
    tasks['sphere_2'] = {
        'objective': OptimizationTasks.shifted_sphere(np.array([1.5] * dimension)),
        'type': 'sphere',
        'description': '平移球函数 - 中心在 [1.5,1.5,1.5,1.5,1.5]'
    }
    
    tasks['sphere_3'] = {
        'objective': OptimizationTasks.shifted_sphere(np.array([0.5] * dimension)),
        'type': 'sphere',
        'description': '平移球函数 - 中心在 [0.5,0.5,0.5,0.5,0.5]'
    }
    
    # 任务组2: 椭球函数（不同旋转和轴比）
    tasks['ellipsoid_1'] = {
        'objective': OptimizationTasks.rotated_ellipsoid(
            np.pi/4, np.array([1, 4, 9, 16, 25])
        ),
        'type': 'ellipsoid',
        'description': '旋转椭球函数 - 45度旋转'
    }
    
    tasks['ellipsoid_2'] = {
        'objective': OptimizationTasks.rotated_ellipsoid(
            np.pi/6, np.array([1, 2, 4, 8, 16])
        ),
        'type': 'ellipsoid',
        'description': '旋转椭球函数 - 30度旋转'
    }
    
    # 任务组3: Rosenbrock函数（不同平移）
    tasks['rosenbrock_1'] = {
        'objective': OptimizationTasks.shifted_rosenbrock(np.array([0.0] * dimension)),
        'type': 'rosenbrock',
        'description': 'Rosenbrock函数 - 无平移'
    }
    
    tasks['rosenbrock_2'] = {
        'objective': OptimizationTasks.shifted_rosenbrock(np.array([0.5] * dimension)),
        'type': 'rosenbrock',
        'description': 'Rosenbrock函数 - 平移0.5'
    }
    
    # 任务组4: 多模态函数
    centers = [np.array([-2, -2] + [0] * (dimension-2)), 
               np.array([2, 2] + [0] * (dimension-2)),
               np.array([0, 0] + [0] * (dimension-2))]
    widths = [1.0, 1.0, 0.5]
    
    tasks['multimodal_1'] = {
        'objective': OptimizationTasks.multimodal_function(centers, widths),
        'type': 'multimodal',
        'description': '多模态函数 - 3个峰'
    }
    
    # 任务组5: 约束优化
    tasks['constrained_1'] = {
        'objective': OptimizationTasks.constrained_quadratic(np.array([0.5] * dimension)),
        'type': 'constrained',
        'description': '约束二次函数'
    }
    
    return tasks, bounds

def run_federated_optimization():
    """运行联邦多任务优化"""
    print("联邦多任务优化示例")
    print("="*50)
    
    # 创建任务
    dimension = 5
    tasks, bounds = create_related_tasks(dimension)
    
    print(f"创建了 {len(tasks)} 个相关优化任务:")
    for task_id, task_info in tasks.items():
        print(f"  {task_id}: {task_info['description']}")
    
    # 创建协调器
    coordinator = FederatedMultiTaskCoordinator(
        global_bounds=bounds,
        max_parallel_tasks=3
    )
    
    # 注册所有任务
    print(f"\n注册任务...")
    for task_id, task_info in tasks.items():
        success = coordinator.register_task(
            task_id=task_id,
            objective_func=task_info['objective'],
            task_type=task_info['type'],
            population_size=20,
            max_fes=200
        )
        if not success:
            print(f"任务 {task_id} 注册失败")
    
    # 执行优化
    print(f"\n开始联邦多任务优化...")
    results = coordinator.optimize_all_tasks(parallel=True)
    
    # 分析结果
    print(f"\n优化完成，分析结果...")
    coordinator.print_summary()
    
    # 保存结果
    coordinator.save_results('federated_optimization_results.json')
    
    return coordinator, results

def analyze_knowledge_transfer(coordinator):
    """分析知识迁移效果"""
    print("\n" + "="*50)
    print("知识迁移分析")
    print("="*50)
    
    # 获取任务关系分析
    relationships = coordinator.analyze_task_relationships()
    
    if 'high_similarity_pairs' in relationships:
        print(f"发现 {len(relationships['high_similarity_pairs'])} 对高相似性任务:")
        for pair in relationships['high_similarity_pairs']:
            print(f"  {pair['task1']} ↔ {pair['task2']}: 相似度 {pair['similarity']:.3f}")
    
    if 'task_clusters' in relationships and relationships['task_clusters']['clusters']:
        print(f"\n任务聚类结果:")
        for i, cluster in enumerate(relationships['task_clusters']['clusters']):
            print(f"  聚类 {i+1}: {cluster}")
    
    # 分析模型选择模式
    print(f"\n模型选择模式分析:")
    for task_id, task_optimizer in coordinator.tasks.items():
        stats = task_optimizer.get_task_statistics()
        print(f"\n任务 {task_id} ({coordinator.task_configs[task_id]['task_type']}):")
        
        # 模型选择统计
        model_counts = stats['model_selection_counts']
        total_selections = sum(model_counts.values())
        if total_selections > 0:
            print(f"  模型选择偏好:")
            for model, count in sorted(model_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_selections) * 100
                print(f"    {model}: {count} 次 ({percentage:.1f}%)")
        
        # 适应性效果
        adaptation_eff = stats.get('adaptation_effectiveness', 0)
        print(f"  适应性效果: {adaptation_eff:.3f}")
        
        # 知识利用权重
        knowledge_weight = stats.get('knowledge_utilization_weight', 0)
        print(f"  知识利用权重: {knowledge_weight:.3f}")

def visualize_results(coordinator, results):
    """可视化结果"""
    print(f"\n生成可视化图表...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('联邦多任务优化结果分析', fontsize=16)
    
    # 1. 收敛曲线对比
    ax1 = axes[0, 0]
    task_types = {}
    colors = {'sphere': 'blue', 'ellipsoid': 'red', 'rosenbrock': 'green', 
              'multimodal': 'orange', 'constrained': 'purple'}
    
    for task_id, task_optimizer in coordinator.tasks.items():
        task_type = coordinator.task_configs[task_id]['task_type']
        if task_type not in task_types:
            task_types[task_type] = []
        task_types[task_type].append(task_id)
        
        history = task_optimizer.history['best_fitness']
        if history:
            color = colors.get(task_type, 'black')
            ax1.plot(history, label=f"{task_id}", color=color, alpha=0.7)
    
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('最佳适应度')
    ax1.set_title('收敛曲线对比')
    ax1.set_yscale('log')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 2. 最终结果对比
    ax2 = axes[0, 1]
    task_names = []
    final_fitness = []
    task_colors = []
    
    for task_id in results.keys():
        if 'error' not in results[task_id]:
            task_names.append(task_id)
            final_fitness.append(results[task_id]['best_fitness'])
            task_type = coordinator.task_configs[task_id]['task_type']
            task_colors.append(colors.get(task_type, 'gray'))
    
    bars = ax2.bar(range(len(task_names)), final_fitness, color=task_colors, alpha=0.7)
    ax2.set_xlabel('任务')
    ax2.set_ylabel('最佳适应度')
    ax2.set_title('最终结果对比')
    ax2.set_yscale('log')
    ax2.set_xticks(range(len(task_names)))
    ax2.set_xticklabels(task_names, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 3. 模型选择分布
    ax3 = axes[0, 2]
    all_model_counts = {}
    for task_id, task_optimizer in coordinator.tasks.items():
        stats = task_optimizer.get_task_statistics()
        for model, count in stats['model_selection_counts'].items():
            if model not in all_model_counts:
                all_model_counts[model] = 0
            all_model_counts[model] += count
    
    if all_model_counts:
        models = list(all_model_counts.keys())
        counts = list(all_model_counts.values())
        ax3.pie(counts, labels=models, autopct='%1.1f%%')
        ax3.set_title('全局模型选择分布')
    
    # 4. 任务类型性能对比
    ax4 = axes[1, 0]
    type_performance = {}
    for task_id in results.keys():
        if 'error' not in results[task_id]:
            task_type = coordinator.task_configs[task_id]['task_type']
            if task_type not in type_performance:
                type_performance[task_type] = []
            type_performance[task_type].append(results[task_id]['best_fitness'])
    
    type_names = list(type_performance.keys())
    type_means = [np.mean(type_performance[t]) for t in type_names]
    type_stds = [np.std(type_performance[t]) if len(type_performance[t]) > 1 else 0 
                 for t in type_names]
    
    bars = ax4.bar(type_names, type_means, yerr=type_stds, 
                   color=[colors.get(t, 'gray') for t in type_names], alpha=0.7)
    ax4.set_xlabel('任务类型')
    ax4.set_ylabel('平均最佳适应度')
    ax4.set_title('任务类型性能对比')
    ax4.set_yscale('log')
    ax4.grid(True, alpha=0.3)
    
    # 5. 适应性效果分析
    ax5 = axes[1, 1]
    adaptation_scores = []
    knowledge_weights = []
    task_labels = []
    
    for task_id, task_optimizer in coordinator.tasks.items():
        stats = task_optimizer.get_task_statistics()
        adaptation_scores.append(stats.get('adaptation_effectiveness', 0))
        knowledge_weights.append(stats.get('knowledge_utilization_weight', 0))
        task_labels.append(task_id)
    
    scatter = ax5.scatter(knowledge_weights, adaptation_scores, 
                         c=[colors.get(coordinator.task_configs[tid]['task_type'], 'gray') 
                            for tid in task_labels], alpha=0.7, s=100)
    
    for i, label in enumerate(task_labels):
        ax5.annotate(label, (knowledge_weights[i], adaptation_scores[i]), 
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    ax5.set_xlabel('知识利用权重')
    ax5.set_ylabel('适应性效果')
    ax5.set_title('知识利用 vs 适应性效果')
    ax5.grid(True, alpha=0.3)
    
    # 6. 任务相似性热图
    ax6 = axes[1, 2]
    relationships = coordinator.analyze_task_relationships()
    if 'similarity_matrix' in relationships and relationships['similarity_matrix']:
        similarity_matrix = np.array(relationships['similarity_matrix'])
        task_ids = relationships['task_ids']
        
        im = ax6.imshow(similarity_matrix, cmap='viridis', aspect='auto')
        ax6.set_xticks(range(len(task_ids)))
        ax6.set_yticks(range(len(task_ids)))
        ax6.set_xticklabels(task_ids, rotation=45, ha='right')
        ax6.set_yticklabels(task_ids)
        ax6.set_title('任务相似性矩阵')
        
        # 添加颜色条
        plt.colorbar(im, ax=ax6)
        
        # 添加数值标注
        for i in range(len(task_ids)):
            for j in range(len(task_ids)):
                text = ax6.text(j, i, f'{similarity_matrix[i, j]:.2f}',
                               ha="center", va="center", color="white", fontsize=8)
    
    plt.tight_layout()
    plt.savefig('federated_multitask_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已保存为 'federated_multitask_analysis.png'")

def main():
    """主函数"""
    # 运行联邦优化
    coordinator, results = run_federated_optimization()
    
    # 分析知识迁移
    analyze_knowledge_transfer(coordinator)
    
    # 可视化结果
    visualize_results(coordinator, results)
    
    print(f"\n联邦多任务优化示例完成!")
    print(f"详细结果已保存到 'federated_optimization_results.json'")
    print(f"可视化图表已保存到 'federated_multitask_analysis.png'")

if __name__ == "__main__":
    main()
