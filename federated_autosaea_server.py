"""
联邦AutoSAEA服务器端实现
专注于多任务知识聚合和模型选择策略优化
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import pickle
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
from autosaea import AutoSAEA, TwoLevelMAB

class TaskKnowledgeBase:
    """任务知识库"""
    
    def __init__(self):
        self.task_solutions = {}  # 每个任务的解决方案库
        self.task_landscapes = {}  # 任务适应度景观特征
        self.model_task_performance = defaultdict(dict)  # 模型在各任务上的性能
        self.task_similarities = {}  # 任务相似性矩阵
        
    def add_task_solutions(self, task_id: str, solutions: List[np.ndarray], 
                          fitness_values: List[float]):
        """添加任务解决方案"""
        if task_id not in self.task_solutions:
            self.task_solutions[task_id] = []
        
        for sol, fit in zip(solutions, fitness_values):
            self.task_solutions[task_id].append({
                'solution': sol,
                'fitness': fit,
                'timestamp': len(self.task_solutions[task_id])
            })
    
    def compute_task_landscape_features(self, task_id: str) -> Dict:
        """计算任务适应度景观特征"""
        if task_id not in self.task_solutions or len(self.task_solutions[task_id]) < 10:
            return {}
        
        solutions = [item['solution'] for item in self.task_solutions[task_id]]
        fitness_values = [item['fitness'] for item in self.task_solutions[task_id]]
        
        solutions_array = np.array(solutions)
        fitness_array = np.array(fitness_values)
        
        features = {
            'fitness_range': np.max(fitness_array) - np.min(fitness_array),
            'fitness_std': np.std(fitness_array),
            'solution_diversity': np.mean(np.std(solutions_array, axis=0)),
            'best_fitness': np.min(fitness_array),
            'convergence_rate': self._compute_convergence_rate(fitness_values),
            'modality_estimate': self._estimate_modality(solutions_array, fitness_array)
        }
        
        self.task_landscapes[task_id] = features
        return features
    
    def _compute_convergence_rate(self, fitness_history: List[float]) -> float:
        """计算收敛速度"""
        if len(fitness_history) < 5:
            return 0.0
        
        # 计算最近一半数据的改进率
        mid_point = len(fitness_history) // 2
        early_best = min(fitness_history[:mid_point])
        late_best = min(fitness_history[mid_point:])
        
        if early_best == 0:
            return 0.0
        
        improvement_rate = (early_best - late_best) / abs(early_best)
        return max(0, improvement_rate)
    
    def _estimate_modality(self, solutions: np.ndarray, fitness: np.ndarray) -> int:
        """估计问题的模态数量"""
        if len(solutions) < 20:
            return 1
        
        # 使用聚类估计局部最优的数量
        try:
            # 选择适应度较好的解进行聚类
            good_indices = fitness <= np.percentile(fitness, 30)
            good_solutions = solutions[good_indices]
            
            if len(good_solutions) < 3:
                return 1
            
            # 尝试不同的聚类数量
            max_clusters = min(10, len(good_solutions) // 2)
            best_k = 1
            best_score = -np.inf
            
            for k in range(1, max_clusters + 1):
                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                labels = kmeans.fit_predict(good_solutions)
                
                # 使用轮廓系数评估聚类质量
                if k > 1:
                    from sklearn.metrics import silhouette_score
                    score = silhouette_score(good_solutions, labels)
                    if score > best_score:
                        best_score = score
                        best_k = k
            
            return best_k
        except:
            return 1

class FederatedAutoSAEAServer:
    """联邦AutoSAEA服务器端"""
    
    def __init__(self, num_tasks: int, global_bounds: np.ndarray):
        self.num_tasks = num_tasks
        self.global_bounds = global_bounds
        self.dimension = global_bounds.shape[0]
        
        # 知识管理
        self.knowledge_base = TaskKnowledgeBase()
        self.global_mab = TwoLevelMAB(alpha=2.0)
        
        # 任务管理
        self.active_tasks = set()
        self.task_types = {}  # 任务类型分类
        self.task_relationships = defaultdict(list)  # 任务关系图
        
        # 全局策略
        self.global_model_preferences = {}
        self.global_criterion_preferences = {}
        self.meta_learning_history = []
        
    def register_task(self, task_id: str, task_type: str = "unknown"):
        """注册新任务"""
        self.active_tasks.add(task_id)
        self.task_types[task_id] = task_type
        
        # 初始化任务特定的MAB
        if task_id not in self.global_model_preferences:
            self.global_model_preferences[task_id] = {
                'GP': 0.0, 'RBF': 0.0, 'PRS': 0.0, 'KNN': 0.0
            }
            self.global_criterion_preferences[task_id] = {
                'LCB': 0.0, 'EI': 0.0, 'prescreening_rbf': 0.0,
                'localsearch_rbf': 0.0, 'prescreening_prs': 0.0,
                'localsearch_prs': 0.0, 'L1_exploitation': 0.0, 'L1_exploration': 0.0
            }
    
    def update_task_knowledge(self, task_id: str, solutions: List[np.ndarray],
                            fitness_values: List[float], model_performance: Dict,
                            criterion_performance: Dict):
        """更新任务知识"""
        # 添加解决方案到知识库
        self.knowledge_base.add_task_solutions(task_id, solutions, fitness_values)
        
        # 更新模型性能
        for model, performance in model_performance.items():
            self.knowledge_base.model_task_performance[task_id][model] = performance
        
        # 更新全局偏好
        self._update_global_preferences(task_id, model_performance, criterion_performance)
        
        # 计算任务特征
        self.knowledge_base.compute_task_landscape_features(task_id)
        
        # 更新任务相似性
        self._update_task_similarities()
    
    def _update_global_preferences(self, task_id: str, model_perf: Dict, criterion_perf: Dict):
        """更新全局模型和准则偏好"""
        # 使用指数移动平均更新
        alpha = 0.1
        
        for model, perf in model_perf.items():
            old_pref = self.global_model_preferences[task_id][model]
            self.global_model_preferences[task_id][model] = (1 - alpha) * old_pref + alpha * perf
        
        for criterion, perf in criterion_perf.items():
            old_pref = self.global_criterion_preferences[task_id][criterion]
            self.global_criterion_preferences[task_id][criterion] = (1 - alpha) * old_pref + alpha * perf
    
    def _update_task_similarities(self):
        """更新任务相似性矩阵"""
        task_ids = list(self.active_tasks)
        n_tasks = len(task_ids)
        
        if n_tasks < 2:
            return
        
        similarity_matrix = np.zeros((n_tasks, n_tasks))
        
        for i, task1 in enumerate(task_ids):
            for j, task2 in enumerate(task_ids):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                elif i < j:  # 只计算上三角
                    sim = self._compute_task_similarity(task1, task2)
                    similarity_matrix[i, j] = sim
                    similarity_matrix[j, i] = sim  # 对称矩阵
        
        # 更新任务关系
        self._update_task_relationships(task_ids, similarity_matrix)
        
        # 存储相似性矩阵
        self.knowledge_base.task_similarities = {
            'task_ids': task_ids,
            'matrix': similarity_matrix
        }
    
    def _compute_task_similarity(self, task1: str, task2: str) -> float:
        """计算两个任务的相似性"""
        similarities = []
        
        # 1. 基于适应度景观特征的相似性
        if task1 in self.knowledge_base.task_landscapes and task2 in self.knowledge_base.task_landscapes:
            features1 = self.knowledge_base.task_landscapes[task1]
            features2 = self.knowledge_base.task_landscapes[task2]
            
            landscape_sim = self._compute_feature_similarity(features1, features2)
            similarities.append(('landscape', landscape_sim, 0.4))
        
        # 2. 基于模型性能的相似性
        if (task1 in self.knowledge_base.model_task_performance and 
            task2 in self.knowledge_base.model_task_performance):
            
            perf1 = list(self.knowledge_base.model_task_performance[task1].values())
            perf2 = list(self.knowledge_base.model_task_performance[task2].values())
            
            if len(perf1) > 0 and len(perf2) > 0:
                perf_sim = cosine_similarity([perf1], [perf2])[0, 0]
                similarities.append(('performance', perf_sim, 0.3))
        
        # 3. 基于解决方案空间的相似性
        if (task1 in self.knowledge_base.task_solutions and 
            task2 in self.knowledge_base.task_solutions):
            
            solutions1 = [item['solution'] for item in self.knowledge_base.task_solutions[task1][-20:]]
            solutions2 = [item['solution'] for item in self.knowledge_base.task_solutions[task2][-20:]]
            
            if len(solutions1) > 0 and len(solutions2) > 0:
                solution_sim = self._compute_solution_space_similarity(solutions1, solutions2)
                similarities.append(('solution_space', solution_sim, 0.3))
        
        # 加权平均
        if not similarities:
            return 0.0
        
        total_weight = sum(weight for _, _, weight in similarities)
        weighted_sim = sum(sim * weight for _, sim, weight in similarities) / total_weight
        
        return max(0.0, min(1.0, weighted_sim))
    
    def _compute_feature_similarity(self, features1: Dict, features2: Dict) -> float:
        """计算特征相似性"""
        common_keys = set(features1.keys()) & set(features2.keys())
        if not common_keys:
            return 0.0
        
        similarities = []
        for key in common_keys:
            val1, val2 = features1[key], features2[key]
            if val1 == 0 and val2 == 0:
                sim = 1.0
            elif val1 == 0 or val2 == 0:
                sim = 0.0
            else:
                sim = 1.0 - abs(val1 - val2) / (abs(val1) + abs(val2))
            similarities.append(sim)
        
        return np.mean(similarities)
    
    def _compute_solution_space_similarity(self, solutions1: List[np.ndarray], 
                                         solutions2: List[np.ndarray]) -> float:
        """计算解空间相似性"""
        # 计算解集合的中心和分布
        center1 = np.mean(solutions1, axis=0)
        center2 = np.mean(solutions2, axis=0)
        
        # 中心距离相似性
        center_dist = np.linalg.norm(center1 - center2)
        max_possible_dist = np.linalg.norm(self.global_bounds[:, 1] - self.global_bounds[:, 0])
        center_sim = 1.0 - center_dist / max_possible_dist
        
        # 分布相似性
        std1 = np.std(solutions1, axis=0)
        std2 = np.std(solutions2, axis=0)
        std_sim = 1.0 - np.mean(np.abs(std1 - std2)) / np.mean(std1 + std2 + 1e-8)
        
        return 0.6 * center_sim + 0.4 * std_sim
    
    def _update_task_relationships(self, task_ids: List[str], similarity_matrix: np.ndarray):
        """更新任务关系图"""
        threshold = 0.7  # 相似性阈值
        
        self.task_relationships.clear()
        
        for i, task1 in enumerate(task_ids):
            for j, task2 in enumerate(task_ids):
                if i != j and similarity_matrix[i, j] > threshold:
                    self.task_relationships[task1].append(task2)
    
    def get_task_initialization_knowledge(self, task_id: str) -> Dict:
        """为任务提供初始化知识"""
        knowledge = {
            'model_preferences': self.global_model_preferences.get(task_id, {}),
            'criterion_preferences': self.global_criterion_preferences.get(task_id, {}),
            'similar_tasks': self.task_relationships.get(task_id, []),
            'initialization_solutions': [],
            'landscape_hints': {}
        }
        
        # 从相似任务中提取初始化解
        similar_tasks = self.task_relationships.get(task_id, [])
        for similar_task in similar_tasks[:3]:  # 最多使用3个相似任务
            if similar_task in self.knowledge_base.task_solutions:
                # 选择最优的几个解
                solutions = self.knowledge_base.task_solutions[similar_task]
                best_solutions = sorted(solutions, key=lambda x: x['fitness'])[:5]
                knowledge['initialization_solutions'].extend([sol['solution'] for sol in best_solutions])
        
        # 提供景观提示
        if similar_tasks:
            landscape_features = []
            for similar_task in similar_tasks:
                if similar_task in self.knowledge_base.task_landscapes:
                    landscape_features.append(self.knowledge_base.task_landscapes[similar_task])
            
            if landscape_features:
                # 平均景观特征作为提示
                avg_features = {}
                for key in landscape_features[0].keys():
                    avg_features[key] = np.mean([f[key] for f in landscape_features])
                knowledge['landscape_hints'] = avg_features
        
        return knowledge
    
    def recommend_model_strategy(self, task_id: str, current_iteration: int) -> Tuple[str, str]:
        """为任务推荐模型和准则策略"""
        # 基于任务历史和相似任务推荐
        model_scores = self.global_model_preferences.get(task_id, {})
        
        # 考虑相似任务的经验
        similar_tasks = self.task_relationships.get(task_id, [])
        for similar_task in similar_tasks:
            similar_prefs = self.global_model_preferences.get(similar_task, {})
            for model, score in similar_prefs.items():
                model_scores[model] = model_scores.get(model, 0.0) + 0.3 * score
        
        # 选择最佳模型
        best_model = max(model_scores.keys(), key=lambda k: model_scores[k])
        
        # 根据模型选择对应的准则
        model_criterion_map = {
            'GP': ['LCB', 'EI'],
            'RBF': ['prescreening_rbf', 'localsearch_rbf'],
            'PRS': ['prescreening_prs', 'localsearch_prs'],
            'KNN': ['L1_exploitation', 'L1_exploration']
        }
        
        available_criteria = model_criterion_map[best_model]
        criterion_scores = self.global_criterion_preferences.get(task_id, {})
        
        best_criterion = max(available_criteria, 
                           key=lambda k: criterion_scores.get(k, 0.0))
        
        return best_model, best_criterion
