"""
Test script for AutoSAEA algorithm
Demonstrates the algorithm on several benchmark functions
"""

import numpy as np
import matplotlib.pyplot as plt
from autosaea import AutoSAEA

def sphere_function(x):
    """Sphere function: f(x) = sum(x_i^2)"""
    return np.sum(x**2)

def rosenbrock_function(x):
    """Rosenbrock function"""
    return np.sum(100 * (x[1:] - x[:-1]**2)**2 + (1 - x[:-1])**2)

def rastrigin_function(x):
    """Rastrigin function"""
    A = 10
    n = len(x)
    return A * n + np.sum(x**2 - A * np.cos(2 * np.pi * x))

def ackley_function(x):
    """Ackley function"""
    a, b, c = 20, 0.2, 2 * np.pi
    n = len(x)
    sum1 = np.sum(x**2)
    sum2 = np.sum(np.cos(c * x))
    return -a * np.exp(-b * np.sqrt(sum1 / n)) - np.exp(sum2 / n) + a + np.exp(1)

def griewank_function(x):
    """Griewank function"""
    sum_part = np.sum(x**2) / 4000
    prod_part = np.prod(np.cos(x / np.sqrt(np.arange(1, len(x) + 1))))
    return sum_part - prod_part + 1

def schwefel_function(x):
    """Schwefel function"""
    return 418.9829 * len(x) - np.sum(x * np.sin(np.sqrt(np.abs(x))))

def test_function(func_name, func, bounds, dimension=10, max_fes=500, population_size=50):
    """Test AutoSAEA on a given function"""
    print(f"\n{'='*60}")
    print(f"Testing {func_name} function (dimension={dimension})")
    print(f"{'='*60}")
    
    # Create bounds array
    bounds_array = np.array([bounds] * dimension)
    
    # Initialize and run AutoSAEA
    optimizer = AutoSAEA(
        objective_func=func,
        bounds=bounds_array,
        population_size=population_size,
        max_fes=max_fes,
        alpha=2.0
    )
    
    # Run optimization
    best_x, best_fitness = optimizer.optimize()
    
    # Get statistics
    stats = optimizer.get_statistics()
    
    print(f"\nResults for {func_name}:")
    print(f"Best solution: {best_x}")
    print(f"Best fitness: {best_fitness:.6f}")
    print(f"Total FEs: {stats['total_fes']}")
    
    print(f"\nModel selection counts:")
    for model, count in stats['model_selection_counts'].items():
        print(f"  {model}: {count}")
    
    print(f"\nFinal model values:")
    for model, value in stats['final_model_values'].items():
        print(f"  {model}: {value:.4f}")
    
    print(f"\nCriterion selection counts:")
    for criterion, count in stats['criterion_selection_counts'].items():
        if count > 0:
            print(f"  {criterion}: {count}")
    
    return optimizer, best_x, best_fitness

def compare_algorithms():
    """Compare AutoSAEA performance on different functions"""
    
    test_functions = [
        ("Sphere", sphere_function, [-5.12, 5.12]),
        ("Rosenbrock", rosenbrock_function, [-2.048, 2.048]),
        ("Rastrigin", rastrigin_function, [-5.12, 5.12]),
        ("Ackley", ackley_function, [-32.768, 32.768]),
        ("Griewank", griewank_function, [-600, 600])
    ]
    
    results = {}
    optimizers = {}
    
    for func_name, func, bounds in test_functions:
        optimizer, best_x, best_fitness = test_function(
            func_name, func, bounds, 
            dimension=10, max_fes=300, population_size=30
        )
        results[func_name] = best_fitness
        optimizers[func_name] = optimizer
    
    # Plot comparison
    plt.figure(figsize=(15, 10))
    
    # Plot convergence curves
    for i, (func_name, optimizer) in enumerate(optimizers.items()):
        plt.subplot(2, 3, i+1)
        plt.plot(optimizer.history['best_fitness'])
        plt.xlabel('Iteration')
        plt.ylabel('Best Fitness')
        plt.title(f'{func_name} Function')
        plt.grid(True)
        plt.yscale('log')
    
    # Plot overall results
    plt.subplot(2, 3, 6)
    func_names = list(results.keys())
    fitness_values = list(results.values())
    plt.bar(func_names, fitness_values)
    plt.xlabel('Function')
    plt.ylabel('Best Fitness (log scale)')
    plt.title('Final Results Comparison')
    plt.yscale('log')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    return results, optimizers

def analyze_model_selection(optimizer):
    """Analyze model and criterion selection patterns"""
    
    plt.figure(figsize=(15, 5))
    
    # Plot model selection over time
    plt.subplot(1, 3, 1)
    models = optimizer.history['selected_models']
    iterations = range(len(models))
    
    model_colors = {'GP': 'red', 'RBF': 'blue', 'PRS': 'green', 'KNN': 'orange'}
    for i, model in enumerate(models):
        plt.scatter(i, model, c=model_colors[model], alpha=0.6)
    
    plt.xlabel('Iteration')
    plt.ylabel('Selected Model')
    plt.title('Model Selection Over Time')
    plt.grid(True, alpha=0.3)
    
    # Plot model values evolution
    plt.subplot(1, 3, 2)
    for model, values in optimizer.history['model_values'].items():
        if len(values) > 0:
            plt.plot(values, label=model, color=model_colors[model])
    
    plt.xlabel('Iteration')
    plt.ylabel('Model Value')
    plt.title('Model Values Evolution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot selection frequency
    plt.subplot(1, 3, 3)
    model_counts = {}
    for model in models:
        model_counts[model] = model_counts.get(model, 0) + 1
    
    plt.pie(model_counts.values(), labels=model_counts.keys(), autopct='%1.1f%%')
    plt.title('Model Selection Distribution')
    
    plt.tight_layout()
    plt.show()

def main():
    """Main function to run tests"""
    print("AutoSAEA Algorithm Test Suite")
    print("=" * 50)
    
    # Test on a single function first
    print("\n1. Single Function Test (Sphere)")
    optimizer, best_x, best_fitness = test_function(
        "Sphere", sphere_function, [-5.12, 5.12], 
        dimension=5, max_fes=200, population_size=20
    )
    
    # Plot convergence and analyze selection
    optimizer.plot_convergence()
    analyze_model_selection(optimizer)
    
    # Compare on multiple functions
    print("\n2. Multi-Function Comparison")
    results, optimizers = compare_algorithms()
    
    print(f"\n{'='*60}")
    print("SUMMARY OF RESULTS")
    print(f"{'='*60}")
    for func_name, fitness in results.items():
        print(f"{func_name:12}: {fitness:.6f}")

if __name__ == "__main__":
    main()
